#!/usr/bin/env python3
"""
测试长文本换行功能
验证包含圆角矩形文本的长段落能够正常换行
"""

import asyncio
import sys
import os

# 添加backend目录到Python路径
sys.path.append('backend')

from app.services.pdf_service import PDFService
from app.models.schemas import LayoutConfig

async def test_long_text_wrapping():
    """测试长文本换行功能"""
    
    # 创建PDF服务实例
    pdf_service = PDFService()
    
    # 创建布局配置
    config = LayoutConfig(
        page_format="A4",
        margin_top=2.0,
        margin_bottom=2.0,
        margin_left=2.0,
        margin_right=2.0,
        font_size=12,
        line_height=1.5,
        paragraph_spacing=12,
        indent_first_line=False,
        font_family="Arial"
    )
    
    # 测试内容 - 包含很长的文本和圆角矩形文本
    test_content = """# 长文本换行测试

本文档测试包含圆角矩形文本的长段落能够正常换行的功能。

## 基本长文本测试

这是一个非常长的段落文本，用来测试当段落内容很长时，包含（（圆角矩形文本））的内容是否能够正常换行。这个段落故意写得很长，以便测试在页面宽度限制下的换行效果。

## 多个圆角矩形文本的长段落

这是另一个很长的段落，包含多个圆角矩形文本：（（第一个标记））和（（第二个标记））以及（（第三个标记））。这个段落的目的是测试当一行中有多个圆角矩形文本时，整个段落是否能够正确换行，而不会出现内容超出页面边界的问题。

## 极长文本测试

这是一个极其长的段落文本，专门用来测试换行功能的极限情况。段落中包含（（北京市第二实验小学附属幼儿园2024年春季学期招生简章及相关注意事项说明））这样的超长圆角矩形文本。我们需要确保即使是这样的长文本，也能够在页面宽度限制下正确换行，不会出现文本溢出或显示异常的问题。

## 混合内容的长段落测试

在实际使用中，我们经常会遇到这样的情况：一个很长的段落中既包含普通文本，也包含需要特殊标记的（（重要信息））。比如这样的句子：根据（（教育部关于义务教育阶段学校招生工作的指导意见））的相关规定，结合（（北京市教育委员会））的具体要求，以及（（海淀区教育局））的实施细则，现将（（2024年春季学期））的招生工作安排通知如下。这样的长段落应该能够正确换行，保持良好的阅读体验。

## 编号列表中的长文本

1. 这是第一个列表项，包含一个很长的文本内容，其中有（（重要的标记信息））需要特殊显示。这个列表项故意写得很长，用来测试在编号列表中，包含圆角矩形文本的长内容是否能够正确换行。

2. 第二个列表项同样包含很长的文本：根据（（学校管理规定））和（（家长手册））的相关要求，所有学生必须遵守（（校园安全管理制度））。这个列表项测试多个圆角矩形文本在编号列表中的换行效果。

3. 第三个列表项测试极长的圆角矩形文本：（（关于进一步加强和改进新时代学校体育工作的意见实施细则及相关配套措施说明文件））应该能够正确换行显示。

## 特殊情况测试

### 行首的圆角矩形文本

（（这是位于行首的圆角矩形文本））后面跟着很长的普通文本内容，用来测试当圆角矩形文本位于行首时的换行效果。

### 行尾的圆角矩形文本

这是一个很长的段落，普通文本内容在前面，圆角矩形文本在最后（（位于行尾的标记文本））。

### 连续的圆角矩形文本

这里有连续的圆角矩形文本：（（第一个））（（第二个））（（第三个））（（第四个））（（第五个））（（第六个））（（第七个））（（第八个））（（第九个））（（第十个））。这样的连续文本应该能够正确换行。

## 结论

如果所有的长文本都能够正确换行，没有出现文本溢出或显示异常，则换行功能实现成功！
"""
    
    try:
        # 生成PDF
        print("正在生成长文本换行测试PDF...")
        pdf_path = await pdf_service.generate_pdf(
            content=test_content,
            config=config,
            filename="long_text_wrapping_test.pdf"
        )
        
        print(f"✅ PDF生成成功: {pdf_path}")
        
        # 检查文件是否存在
        if os.path.exists(pdf_path):
            file_size = os.path.getsize(pdf_path)
            print(f"📄 文件大小: {file_size} 字节")
            
            # 尝试获取页数
            try:
                page_count = await pdf_service.get_page_count(pdf_path)
                print(f"📖 页数: {page_count}")
            except:
                print("📖 无法获取页数")
                
        else:
            print("❌ PDF文件未找到")
            
        print("\n" + "="*70)
        print("🎯 长文本换行测试完成！")
        print("✅ 测试内容包括：")
        print("   - 基本长文本换行")
        print("   - 多个圆角矩形文本的换行")
        print("   - 编号列表中的长文本换行")
        print("   - 特殊位置的圆角矩形文本")
        print("请打开PDF文件检查所有文本是否正确换行。")
        print("="*70)
            
    except Exception as e:
        print(f"❌ 生成PDF失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_long_text_wrapping())
