<!DOCTYPE html>
<html>
<head>
    <title>测试文档上传</title>
</head>
<body>
    <h1>PrintMind 文档上传测试</h1>
    
    <form id="uploadForm" enctype="multipart/form-data">
        <input type="file" id="fileInput" accept=".md,.txt,.docx" />
        <button type="submit">上传文件</button>
    </form>
    
    <div id="result"></div>
    
    <script>
        document.getElementById('uploadForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) {
                alert('请选择文件');
                return;
            }
            
            const formData = new FormData();
            formData.append('file', file);
            
            try {
                const response = await fetch('http://localhost:8000/api/documents/upload', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                document.getElementById('result').innerHTML = `
                    <h3>上传成功！</h3>
                    <p>文件ID: ${result.file_id}</p>
                    <p>文件名: ${result.filename}</p>
                    <p>文件大小: ${result.file_size} bytes</p>
                    <h4>内容预览:</h4>
                    <pre>${result.markdown_content}</pre>
                `;
            } catch (error) {
                document.getElementById('result').innerHTML = `
                    <h3>上传失败</h3>
                    <p>错误: ${error.message}</p>
                `;
                console.error('上传错误:', error);
            }
        });
    </script>
</body>
</html>
