#!/usr/bin/env python3
"""
创建一级标题背景图片
根据提供的橙色圆角矩形设计创建PNG图片
"""

from PIL import Image, ImageDraw
import os

def create_heading_background():
    """创建一级标题背景图片"""
    
    # 图片尺寸
    width = 800
    height = 120
    
    # 创建图片
    img = Image.new('RGBA', (width, height), (255, 255, 255, 0))  # 透明背景
    draw = ImageDraw.Draw(img)
    
    # 橙色渐变色彩
    orange_colors = [
        (255, 140, 0),    # #FF8C00
        (255, 165, 0),    # #FFA500
        (255, 127, 0),    # #FF7F00
    ]
    
    # 圆角半径
    corner_radius = 50
    
    # 主体矩形区域
    main_rect = [corner_radius, 20, width - corner_radius, height - 20]
    
    # 绘制主体圆角矩形
    # 由于PIL的圆角矩形支持有限，我们分段绘制
    
    # 中间矩形部分
    draw.rectangle([corner_radius, 20, width - corner_radius, height - 20], 
                  fill=orange_colors[1])
    
    # 左侧圆形
    draw.ellipse([0, 20, corner_radius * 2, height - 20], 
                fill=orange_colors[0])
    
    # 右侧圆形  
    draw.ellipse([width - corner_radius * 2, 20, width, height - 20], 
                fill=orange_colors[0])
    
    # 添加渐变效果（简化版）
    for i in range(corner_radius, width - corner_radius, 10):
        alpha = int(255 * (1 - abs(i - width/2) / (width/2)) * 0.3)
        overlay_color = (*orange_colors[2], alpha)
        draw.rectangle([i, 25, i + 8, height - 25], 
                      fill=overlay_color)
    
    # 确保目录存在
    os.makedirs('backend/assets', exist_ok=True)
    
    # 保存图片
    img.save('backend/assets/heading_background.png', 'PNG')
    print("✅ 一级标题背景图片已创建: backend/assets/heading_background.png")
    
    # 创建一个较小的版本用于测试
    small_img = img.resize((400, 60), Image.Resampling.LANCZOS)
    small_img.save('backend/assets/heading_background_small.png', 'PNG')
    print("✅ 小尺寸背景图片已创建: backend/assets/heading_background_small.png")
    
    return True

if __name__ == "__main__":
    print("🎨 创建一级标题背景图片...")
    success = create_heading_background()
    if success:
        print("🎉 背景图片创建完成！")
    else:
        print("❌ 背景图片创建失败")
