<template>
  <div class="pdf-preview h-full flex flex-col">
    <!-- 预览工具栏 -->
    <div class="preview-toolbar flex items-center justify-between p-3 border-b border-gray-200 bg-gray-50">
      <div class="flex items-center space-x-2">
        <button
          @click="refreshPreview"
          :disabled="isLoading"
          class="px-3 py-1 text-sm bg-primary-100 text-primary-700 rounded hover:bg-primary-200 disabled:opacity-50"
        >
          {{ isLoading ? '生成中...' : '刷新预览' }}
        </button>

        <span class="text-sm text-gray-600">PDF预览</span>
      </div>
    </div>

    <!-- 预览内容区域 -->
    <div class="preview-content flex-1 overflow-auto bg-gray-100">
      <!-- 加载状态 -->
      <div v-if="isLoading" class="flex items-center justify-center h-full">
        <div class="text-center">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
          <p class="text-gray-600">正在生成预览...</p>
        </div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="flex items-center justify-center h-full">
        <div class="text-center">
          <div class="text-red-500 text-4xl mb-4">⚠️</div>
          <p class="text-red-600 mb-4">{{ error }}</p>
          <button @click="refreshPreview" class="btn-primary">
            重试
          </button>
        </div>
      </div>

      <!-- PDF预览 -->
      <div
        v-else-if="pdfPreview"
        class="pdf-preview-container p-4"
      >
        <iframe
          :src="pdfPreview"
          class="w-full h-full border-0 bg-white shadow-lg mx-auto"
          style="min-height: 600px;"
        ></iframe>
      </div>

      <!-- 空状态 -->
      <div v-else class="flex items-center justify-center h-full">
        <div class="text-center text-gray-500">
          <div class="text-4xl mb-4">📄</div>
          <p>输入内容后将显示预览</p>
        </div>
      </div>
    </div>

    <!-- 预览信息 -->
    <div v-if="previewInfo" class="preview-info p-3 border-t border-gray-200 bg-gray-50">
      <div class="flex items-center justify-between text-xs text-gray-600">
        <div class="flex items-center space-x-4">
          <span>页面: {{ previewInfo.pageFormat }}</span>
          <span>字体: {{ previewInfo.fontSize }}pt</span>
          <span>行高: {{ previewInfo.lineHeight }}</span>
        </div>
        <div class="flex items-center space-x-4">
          <span>预计页数: {{ estimatedPages }}</span>
          <span>字数: {{ wordCount }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { pdfAPI } from '@/utils/api'
import type { LayoutConfig } from '@/types/layout'

// 组件属性
interface Props {
  content: string
  config: LayoutConfig
}

const props = defineProps<Props>()

// 响应式数据
const isLoading = ref(false)
const error = ref('')
const pdfPreview = ref('')

// 计算属性
const previewInfo = computed(() => {
  if (!props.config) return null
  
  return {
    pageFormat: props.config.page_format,
    fontSize: props.config.font_size,
    lineHeight: props.config.line_height
  }
})

const wordCount = computed(() => {
  if (!props.content) return 0
  
  // 简单的中英文字数统计
  const chinese = (props.content.match(/[\u4e00-\u9fff]/g) || []).length
  const english = (props.content.match(/[a-zA-Z]+/g) || []).join('').length
  return chinese + Math.ceil(english / 4)
})

const estimatedPages = computed(() => {
  if (!props.content || !props.config) return 1
  
  // 简单的页数估算
  const wordsPerPage = 500 // 假设每页500字
  return Math.max(1, Math.ceil(wordCount.value / wordsPerPage))
})

// 方法
const refreshPreview = async () => {
  // 检查是否有内容
  if (!props.content || !props.content.trim()) {
    pdfPreview.value = ''
    error.value = ''
    return
  }

  isLoading.value = true
  error.value = ''

  try {
    await generatePDFPreview()
  } catch (err: any) {
    console.error('预览生成失败:', err)
    error.value = err.response?.data?.detail || err.message || '预览生成失败'
  } finally {
    isLoading.value = false
  }
}

const generatePDFPreview = async () => {
  try {
    const response = await pdfAPI.preview({
      content: props.content,
      layout_config: props.config
    })
    
    if (response.success && response.pdf_data) {
      // 创建PDF blob URL
      const pdfBlob = new Blob(
        [Uint8Array.from(atob(response.pdf_data), c => c.charCodeAt(0))],
        { type: 'application/pdf' }
      )
      pdfPreview.value = URL.createObjectURL(pdfBlob)
    } else {
      throw new Error('PDF预览生成失败')
    }
  } catch (error) {
    throw error
  }
}

// 监听内容和配置变化
watch([() => props.content, () => props.config], () => {
  // 防抖处理
  clearTimeout(refreshPreview.timeout)
  refreshPreview.timeout = setTimeout(() => {
    if (props.content && props.content.trim()) {
      refreshPreview()
    } else {
      // 清空预览
      pdfPreview.value = ''
      error.value = ''
    }
  }, 1000)
}, { deep: true })

// 组件挂载时不自动生成预览，等待内容输入

// 清理资源
const cleanup = () => {
  if (pdfPreview.value && pdfPreview.value.startsWith('blob:')) {
    URL.revokeObjectURL(pdfPreview.value)
  }
}

// 组件卸载时清理
onMounted(() => {
  return cleanup
})
</script>

<style scoped>
.pdf-preview {
  @apply border border-gray-300 rounded-lg overflow-hidden;
}



.pdf-preview-container {
  @apply flex justify-center;
  min-height: 100%;
}


</style>
