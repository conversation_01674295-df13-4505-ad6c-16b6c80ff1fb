<template>
  <div class="file-upload">
    <!-- 拖拽上传区域 -->
    <div
      class="upload-area"
      :class="{ 'drag-over': isDragOver, 'uploading': isUploading }"
      @drop="handleDrop"
      @dragover.prevent="handleDragOver"
      @dragleave="handleDragLeave"
      @click="triggerFileInput"
    >
      <div class="upload-content">
        <div v-if="!isUploading" class="upload-icon">
          <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                  d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
          </svg>
        </div>
        
        <div v-if="isUploading" class="upload-spinner">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
        </div>

        <div class="upload-text">
          <p v-if="!isUploading" class="text-lg font-medium text-gray-900">
            拖拽文件到此处或点击上传
          </p>
          <p v-else class="text-lg font-medium text-gray-900">
            正在上传文件...
          </p>
          <p class="text-sm text-gray-500 mt-2">
            支持 .md, .docx, .txt 格式，最大 50MB
          </p>
        </div>
      </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInput"
      type="file"
      accept=".md,.docx,.txt"
      class="hidden"
      @change="handleFileSelect"
    />

    <!-- 上传历史 -->
    <div v-if="uploadHistory.length > 0" class="upload-history mt-4">
      <h4 class="text-sm font-medium text-gray-900 mb-2">最近上传</h4>
      <div class="space-y-2">
        <div
          v-for="file in uploadHistory"
          :key="file.file_id"
          class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
        >
          <div class="flex items-center">
            <div class="file-icon mr-3">
              <svg class="w-5 h-5 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                <path d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" />
              </svg>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-900">{{ file.filename }}</p>
              <p class="text-xs text-gray-500">{{ formatFileSize(file.file_size) }}</p>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <button
              @click="loadFile(file)"
              class="text-primary-600 hover:text-primary-700 text-sm font-medium"
            >
              加载
            </button>
            <button
              @click="deleteFile(file.file_id)"
              class="text-red-600 hover:text-red-700 text-sm font-medium"
            >
              删除
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="errorMessage" class="error-message mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
      <p class="text-sm text-red-600">{{ errorMessage }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { documentAPI } from '@/utils/api'
import type { DocumentUploadResponse } from '@/types/layout'

// 组件事件
const emit = defineEmits<{
  'file-uploaded': [content: string]
}>()

// 响应式数据
const isDragOver = ref(false)
const isUploading = ref(false)
const errorMessage = ref('')
const fileInput = ref<HTMLInputElement>()
const uploadHistory = ref<DocumentUploadResponse[]>([])

// 拖拽处理
const handleDragOver = (e: DragEvent) => {
  e.preventDefault()
  isDragOver.value = true
}

const handleDragLeave = () => {
  isDragOver.value = false
}

const handleDrop = (e: DragEvent) => {
  e.preventDefault()
  isDragOver.value = false
  
  const files = e.dataTransfer?.files
  if (files && files.length > 0) {
    uploadFile(files[0])
  }
}

// 文件选择处理
const triggerFileInput = () => {
  if (!isUploading.value) {
    fileInput.value?.click()
  }
}

const handleFileSelect = (e: Event) => {
  const target = e.target as HTMLInputElement
  const files = target.files
  if (files && files.length > 0) {
    uploadFile(files[0])
  }
}

// 文件上传
const uploadFile = async (file: File) => {
  if (isUploading.value) return

  // 验证文件类型
  const allowedTypes = ['.md', '.docx', '.txt']
  const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase()
  
  if (!allowedTypes.includes(fileExtension)) {
    errorMessage.value = '不支持的文件类型'
    return
  }

  // 验证文件大小 (50MB)
  if (file.size > 50 * 1024 * 1024) {
    errorMessage.value = '文件大小超过50MB限制'
    return
  }

  isUploading.value = true
  errorMessage.value = ''

  try {
    const response = await documentAPI.upload(file)
    
    // 添加到历史记录
    uploadHistory.value.unshift(response)
    if (uploadHistory.value.length > 5) {
      uploadHistory.value = uploadHistory.value.slice(0, 5)
    }

    // 发送文件内容给父组件
    emit('file-uploaded', response.markdown_content)

    // 保存到本地存储
    saveUploadHistory()

  } catch (error: any) {
    console.error('文件上传失败:', error)
    errorMessage.value = error.response?.data?.detail || '文件上传失败'
  } finally {
    isUploading.value = false
    // 清空文件输入
    if (fileInput.value) {
      fileInput.value.value = ''
    }
  }
}

// 加载历史文件
const loadFile = (file: DocumentUploadResponse) => {
  emit('file-uploaded', file.markdown_content)
}

// 删除文件
const deleteFile = async (fileId: string) => {
  try {
    await documentAPI.delete(fileId)
    uploadHistory.value = uploadHistory.value.filter(f => f.file_id !== fileId)
    saveUploadHistory()
  } catch (error) {
    console.error('删除文件失败:', error)
  }
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 保存上传历史到本地存储
const saveUploadHistory = () => {
  localStorage.setItem('upload_history', JSON.stringify(uploadHistory.value))
}

// 加载上传历史
const loadUploadHistory = () => {
  const saved = localStorage.getItem('upload_history')
  if (saved) {
    try {
      uploadHistory.value = JSON.parse(saved)
    } catch (error) {
      console.error('加载上传历史失败:', error)
    }
  }
}

// 组件挂载时加载历史
onMounted(() => {
  loadUploadHistory()
})
</script>

<style scoped>
.upload-area {
  border: 2px dashed #d1d5db;
  border-radius: 0.5rem;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
}

.upload-area:hover {
  border-color: #60a5fa;
  background-color: #eff6ff;
}

.upload-area.drag-over {
  border-color: #3b82f6;
  background-color: #dbeafe;
}

.upload-area.uploading {
  cursor: not-allowed;
  opacity: 0.75;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.file-icon {
  flex-shrink: 0;
}
</style>
