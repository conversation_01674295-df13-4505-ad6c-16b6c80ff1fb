<template>
  <div class="export-control">
    <!-- 导出选项 -->
    <div class="export-options mb-4">
      <label class="block text-sm font-medium text-gray-700 mb-2">
        导出格式
      </label>
      <div class="grid grid-cols-2 gap-2">
        <button
          @click="exportFormat = 'pdf'"
          :class="[
            'p-3 border rounded-lg text-sm font-medium transition-colors',
            exportFormat === 'pdf' 
              ? 'border-primary-500 bg-primary-50 text-primary-700' 
              : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
          ]"
        >
          📄 PDF
        </button>

      </div>
    </div>

    <!-- 文件名设置 -->
    <div class="filename-setting mb-4">
      <label class="block text-sm font-medium text-gray-700 mb-1">
        文件名
      </label>
      <input
        v-model="filename"
        type="text"
        placeholder="输入文件名..."
        class="input-field"
      />
      <p class="text-xs text-gray-500 mt-1">
        不需要包含文件扩展名
      </p>
    </div>

    <!-- 导出质量设置 -->
    <div v-if="exportFormat === 'pdf'" class="quality-settings mb-4">
      <label class="block text-sm font-medium text-gray-700 mb-2">
        导出质量
      </label>
      <div class="space-y-2">
        <label class="flex items-center">
          <input
            v-model="exportQuality"
            type="radio"
            value="draft"
            class="mr-2"
          />
          <span class="text-sm">草稿 (150 DPI)</span>
        </label>
        <label class="flex items-center">
          <input
            v-model="exportQuality"
            type="radio"
            value="standard"
            class="mr-2"
          />
          <span class="text-sm">标准 (300 DPI)</span>
        </label>
        <label class="flex items-center">
          <input
            v-model="exportQuality"
            type="radio"
            value="high"
            class="mr-2"
          />
          <span class="text-sm">高质量 (600 DPI)</span>
        </label>
      </div>
    </div>

    <!-- 导出按钮 -->
    <div class="export-actions space-y-3">
      <button
        @click="exportDocument"
        :disabled="!canExport || isExporting"
        class="w-full btn-primary"
      >
        <span v-if="!isExporting">
          导出PDF
        </span>
        <span v-else class="flex items-center justify-center">
          <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
          导出中...
        </span>
      </button>

      <button
        @click="quickExport"
        :disabled="!canExport || isExporting"
        class="w-full btn-secondary"
      >
        快速导出PDF
      </button>
    </div>

    <!-- 导出历史 -->
    <div v-if="exportHistory.length > 0" class="export-history mt-6">
      <h4 class="text-sm font-medium text-gray-900 mb-3">导出历史</h4>
      <div class="space-y-2 max-h-40 overflow-y-auto">
        <div
          v-for="item in exportHistory"
          :key="item.id"
          class="flex items-center justify-between p-2 bg-gray-50 rounded text-sm"
        >
          <div class="flex items-center">
            <span class="mr-2">
              {{ item.format === 'pdf' ? '📄' : '🌐' }}
            </span>
            <div>
              <p class="font-medium">{{ item.filename }}</p>
              <p class="text-xs text-gray-500">
                {{ formatDate(item.timestamp) }}
              </p>
            </div>
          </div>
          <div class="flex items-center space-x-1">
            <button
              @click="downloadFile(item)"
              class="text-primary-600 hover:text-primary-700 text-xs"
            >
              下载
            </button>
            <button
              @click="deleteExport(item.id)"
              class="text-red-600 hover:text-red-700 text-xs"
            >
              删除
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 导出统计 -->
    <div class="export-stats mt-4 p-3 bg-blue-50 rounded-lg">
      <h4 class="text-sm font-medium text-blue-900 mb-2">导出信息</h4>
      <div class="text-xs text-blue-800 space-y-1">
        <div class="flex justify-between">
          <span>预计页数:</span>
          <span>{{ estimatedPages }}</span>
        </div>
        <div class="flex justify-between">
          <span>字数统计:</span>
          <span>{{ wordCount }}</span>
        </div>
        <div class="flex justify-between">
          <span>预计大小:</span>
          <span>{{ estimatedSize }}</span>
        </div>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="errorMessage" class="error-message mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
      <p class="text-sm text-red-600">{{ errorMessage }}</p>
    </div>

    <!-- 成功提示 -->
    <div v-if="successMessage" class="success-message mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
      <p class="text-sm text-green-600">{{ successMessage }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { pdfAPI } from '@/utils/api'
import type { LayoutConfig } from '@/types/layout'

// 组件属性
interface Props {
  content: string
  config: LayoutConfig
}

const props = defineProps<Props>()

// 导出历史项接口
interface ExportHistoryItem {
  id: string
  filename: string
  format: 'pdf'
  timestamp: number
  url?: string
  size?: number
}

// 响应式数据
const exportFormat = ref<'pdf'>('pdf')
const exportQuality = ref('standard')
const filename = ref('')
const isExporting = ref(false)
const errorMessage = ref('')
const successMessage = ref('')
const exportHistory = ref<ExportHistoryItem[]>([])

// 计算属性
const canExport = computed(() => {
  return props.content.trim().length > 0 && filename.value.trim().length > 0
})

const wordCount = computed(() => {
  if (!props.content) return 0
  
  const chinese = (props.content.match(/[\u4e00-\u9fff]/g) || []).length
  const english = (props.content.match(/[a-zA-Z]+/g) || []).join('').length
  return chinese + Math.ceil(english / 4)
})

const estimatedPages = computed(() => {
  const wordsPerPage = 500
  return Math.max(1, Math.ceil(wordCount.value / wordsPerPage))
})

const estimatedSize = computed(() => {
  // PDF大小估算：每页约100KB
  const sizeKB = estimatedPages.value * 100
  return sizeKB > 1024 ? `${(sizeKB / 1024).toFixed(1)}MB` : `${sizeKB}KB`
})

// 方法
const exportDocument = async () => {
  if (!canExport.value || isExporting.value) return

  isExporting.value = true
  errorMessage.value = ''
  successMessage.value = ''

  try {
    await exportPDF()
  } catch (error: any) {
    console.error('导出失败:', error)
    errorMessage.value = error.response?.data?.detail || '导出失败'
  } finally {
    isExporting.value = false
  }
}

const exportPDF = async () => {
  // 根据质量设置调整DPI
  const qualityDPI = {
    draft: 150,
    standard: 300,
    high: 600
  }

  const exportConfig = {
    ...props.config,
    dpi: qualityDPI[exportQuality.value as keyof typeof qualityDPI]
  }

  const response = await pdfAPI.generate({
    content: props.content,
    layout_config: exportConfig,
    filename: filename.value
  })

  // 添加到导出历史
  const historyItem: ExportHistoryItem = {
    id: Date.now().toString(),
    filename: filename.value + '.pdf',
    format: 'pdf',
    timestamp: Date.now(),
    url: response.pdf_url,
    size: response.file_size
  }

  exportHistory.value.unshift(historyItem)
  saveExportHistory()

  // 自动下载
  const link = document.createElement('a')
  link.href = response.pdf_url
  link.download = historyItem.filename
  link.click()

  successMessage.value = `PDF导出成功！文件大小: ${formatFileSize(response.file_size)}`
}

const quickExport = async () => {
  if (!props.content.trim()) return

  // 使用默认文件名
  const defaultFilename = filename.value || `document_${Date.now()}`
  const originalFilename = filename.value
  
  filename.value = defaultFilename
  exportFormat.value = 'pdf'
  exportQuality.value = 'standard'

  await exportDocument()

  // 恢复原文件名
  if (!originalFilename) {
    filename.value = ''
  }
}

const downloadFile = (item: ExportHistoryItem) => {
  if (item.url) {
    const link = document.createElement('a')
    link.href = item.url
    link.download = item.filename
    link.click()
  }
}

const deleteExport = (id: string) => {
  const index = exportHistory.value.findIndex(item => item.id === id)
  if (index > -1) {
    const item = exportHistory.value[index]
    
    // 清理blob URL
    if (item.url && item.url.startsWith('blob:')) {
      URL.revokeObjectURL(item.url)
    }
    
    exportHistory.value.splice(index, 1)
    saveExportHistory()
  }
}

const formatDate = (timestamp: number): string => {
  const date = new Date(timestamp)
  return date.toLocaleString('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const saveExportHistory = () => {
  // 只保存最近10个导出记录
  const historyToSave = exportHistory.value.slice(0, 10).map(item => ({
    ...item,
    url: undefined // 不保存URL到localStorage
  }))
  
  localStorage.setItem('export_history', JSON.stringify(historyToSave))
}

const loadExportHistory = () => {
  const saved = localStorage.getItem('export_history')
  if (saved) {
    try {
      exportHistory.value = JSON.parse(saved)
    } catch (error) {
      console.error('加载导出历史失败:', error)
    }
  }
}

// 自动清理成功/错误消息
watch([successMessage, errorMessage], () => {
  if (successMessage.value) {
    setTimeout(() => {
      successMessage.value = ''
    }, 5000)
  }
  if (errorMessage.value) {
    setTimeout(() => {
      errorMessage.value = ''
    }, 8000)
  }
})

// 组件挂载时加载历史
loadExportHistory()
</script>

<style scoped>
.export-control {
  @apply space-y-4;
}
</style>
