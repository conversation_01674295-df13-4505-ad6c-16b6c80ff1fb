{"name": "@babel/helper-optimise-call-expression", "version": "7.27.1", "description": "Helper function to optimise call expression", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-optimise-call-expression"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-optimise-call-expression", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "dependencies": {"@babel/types": "^7.27.1"}, "devDependencies": {"@babel/generator": "^7.27.1", "@babel/parser": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs"}