{"version": 3, "names": ["_defineProperty", "require", "ownKeys", "object", "enumerableOnly", "keys", "Object", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread2", "target", "i", "arguments", "length", "source", "for<PERSON>ach", "key", "defineProperty", "getOwnPropertyDescriptors", "defineProperties"], "sources": ["../../src/helpers/objectSpread2.ts"], "sourcesContent": ["/* @minVersion 7.5.0 */\n\nimport defineProperty from \"./defineProperty.ts\";\n\n// This function is different to \"Reflect.ownKeys\". The enumerableOnly\n// filters on symbol properties only. Returned string properties are always\n// enumerable. It is good to use in objectSpread.\n\nfunction ownKeys(\n  object: object,\n  enumerableOnly?: boolean | undefined,\n): (string | symbol)[] {\n  var keys: (string | symbol)[] = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        // sym already comes from `Object.getOwnPropertySymbols`, so getOwnPropertyDescriptor should always be defined\n        return Object.getOwnPropertyDescriptor(object, sym)!.enumerable;\n      });\n    }\n    keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\n\ntype Intersection<R extends any[]> = R extends [infer H, ...infer S]\n  ? H & Intersection<S>\n  : unknown;\n\nexport default function _objectSpread2<T extends object, U extends unknown[]>(\n  target: T,\n  ...sources: U\n): T & Intersection<U>;\nexport default function _objectSpread2(target: object) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(\n          target,\n          key,\n          // key already comes from ownKeys, so getOwnPropertyDescriptor should always be defined\n          Object.getOwnPropertyDescriptor(source, key)!,\n        );\n      });\n    }\n  }\n  return target;\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,eAAA,GAAAC,OAAA;AAMA,SAASC,OAAOA,CACdC,MAAc,EACdC,cAAoC,EACf;EACrB,IAAIC,IAAyB,GAAGC,MAAM,CAACD,IAAI,CAACF,MAAM,CAAC;EACnD,IAAIG,MAAM,CAACC,qBAAqB,EAAE;IAChC,IAAIC,OAAO,GAAGF,MAAM,CAACC,qBAAqB,CAACJ,MAAM,CAAC;IAClD,IAAIC,cAAc,EAAE;MAClBI,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;QAEtC,OAAOJ,MAAM,CAACK,wBAAwB,CAACR,MAAM,EAAEO,GAAG,CAAC,CAAEE,UAAU;MACjE,CAAC,CAAC;IACJ;IACAP,IAAI,CAACQ,IAAI,CAACC,KAAK,CAACT,IAAI,EAAEG,OAAO,CAAC;EAChC;EACA,OAAOH,IAAI;AACb;AAUe,SAASU,cAAcA,CAACC,MAAc,EAAE;EACrD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IACzC,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC,IAAI,IAAI,GAAGC,SAAS,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC;IACrD,IAAIA,CAAC,GAAG,CAAC,EAAE;MACTf,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,EAAE,IAAI,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;QACnD,IAAAC,uBAAc,EAACP,MAAM,EAAEM,GAAG,EAAEF,MAAM,CAACE,GAAG,CAAC,CAAC;MAC1C,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIhB,MAAM,CAACkB,yBAAyB,EAAE;MAC3ClB,MAAM,CAACmB,gBAAgB,CAACT,MAAM,EAAEV,MAAM,CAACkB,yBAAyB,CAACJ,MAAM,CAAC,CAAC;IAC3E,CAAC,MAAM;MACLlB,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;QAC7ChB,MAAM,CAACiB,cAAc,CACnBP,MAAM,EACNM,GAAG,EAEHhB,MAAM,CAACK,wBAAwB,CAACS,MAAM,EAAEE,GAAG,CAC7C,CAAC;MACH,CAAC,CAAC;IACJ;EACF;EACA,OAAON,MAAM;AACf", "ignoreList": []}