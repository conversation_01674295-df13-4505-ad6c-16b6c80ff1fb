{"version": 3, "names": ["<PERSON><PERSON>", "getCode", "getScope", "addHelper", "Error", "buildError", "node", "msg", "TypeError", "exports", "default"], "sources": ["../src/hub.ts"], "sourcesContent": ["import type Scope from \"./scope/index.ts\";\nimport type { Node } from \"@babel/types\";\n\nexport interface HubInterface {\n  getCode(): string | void;\n  getScope(): Scope | void;\n  addHelper(name: string): any;\n  buildError(node: Node, msg: string, Error: new () => Error): Error;\n}\n\nexport default class Hub implements HubInterface {\n  getCode() {}\n\n  getScope() {}\n\n  addHelper() {\n    throw new Error(\"Helpers are not supported by the default hub.\");\n  }\n\n  buildError(node: Node, msg: string, Error = TypeError): Error {\n    return new Error(msg);\n  }\n}\n"], "mappings": ";;;;;;AAUe,MAAMA,GAAG,CAAyB;EAC/CC,OAAOA,CAAA,EAAG,CAAC;EAEXC,QAAQA,CAAA,EAAG,CAAC;EAEZC,SAASA,CAAA,EAAG;IACV,MAAM,IAAIC,KAAK,CAAC,+CAA+C,CAAC;EAClE;EAEAC,UAAUA,CAACC,IAAU,EAAEC,GAAW,EAAEH,KAAK,GAAGI,SAAS,EAAS;IAC5D,OAAO,IAAIJ,KAAK,CAACG,GAAG,CAAC;EACvB;AACF;AAACE,OAAA,CAAAC,OAAA,GAAAV,GAAA", "ignoreList": []}