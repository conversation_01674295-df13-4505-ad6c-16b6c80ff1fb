{"version": 3, "names": ["_t", "require", "_template", "_visitors", "_context", "arrowFunctionExpression", "assignmentExpression", "binaryExpression", "blockStatement", "callExpression", "conditionalExpression", "expressionStatement", "identifier", "isIdentifier", "jsxIdentifier", "logicalExpression", "LOGICAL_OPERATORS", "memberExpression", "metaProperty", "numericLiteral", "objectExpression", "restElement", "returnStatement", "sequenceExpression", "spreadElement", "stringLiteral", "super", "_super", "thisExpression", "toExpression", "unaryExpression", "toBindingIdentifierName", "isFunction", "isAssignmentPattern", "isRestElement", "getFunctionName", "cloneNode", "variableDeclaration", "variableDeclarator", "exportNamedDeclaration", "exportSpecifier", "inherits", "toCom<PERSON><PERSON>ey", "key", "isMemberExpression", "node", "property", "isProperty", "isMethod", "ReferenceError", "computed", "name", "ensureBlock", "body", "get", "bodyNode", "Array", "isArray", "Error", "isBlockStatement", "statements", "stringPath", "<PERSON><PERSON><PERSON>", "isStatement", "push", "parentPath", "setup", "call", "exports", "arrowFunctionToShadowed", "isArrowFunctionExpression", "arrowFunctionToExpression", "unwrapFunctionEnvironment", "isFunctionExpression", "isFunctionDeclaration", "buildCodeFrameError", "hoistFunctionEnvironment", "setType", "path", "type", "allowInsertArrow", "allowInsertArrowWithRest", "noNewArrows", "_arguments$", "arguments", "specCompliant", "self", "_self$ensureFunctionN", "ensureFunctionName", "thisBinding", "fnPath", "fn", "checkBinding", "scope", "generateUidIdentifier", "id", "init", "unshiftContainer", "hub", "addHelper", "replaceWith", "getSuperCallsVisitor", "environmentVisitor", "CallExpression", "child", "allSuperCalls", "is<PERSON><PERSON><PERSON>", "arrowParent", "thisEnvFn", "findParent", "p", "isProgram", "isClassProperty", "static", "isClassPrivateProperty", "inConstructor", "isClassMethod", "kind", "thisPaths", "argumentsPaths", "newTargetPaths", "superProps", "superCalls", "getScopeInformation", "length", "traverse", "superBinding", "getSuper<PERSON><PERSON><PERSON>", "for<PERSON>ach", "superCall", "callee", "loc", "argumentsBinding", "getBinding", "args", "buildUndefinedNode", "<PERSON><PERSON><PERSON><PERSON>", "argsRef", "newTargetBinding", "targetChild", "targetRef", "flatSuperProps", "reduce", "acc", "superProp", "concat", "standardizeSuperProperty", "superParent<PERSON>ath", "isAssignment", "isAssignmentExpression", "left", "isCall", "isCallExpression", "isTaggedTemplate", "isTaggedTemplateExpression", "tag", "getSuperPropBinding", "value", "right", "getThis<PERSON><PERSON>ing", "hasSuperClass", "<PERSON><PERSON><PERSON><PERSON>", "thisRef", "isJSX", "isLogicalOp", "op", "includes", "operator", "assignment<PERSON><PERSON>", "slice", "isLogicalAssignment", "tmp", "generateDeclaredUidIdentifier", "object", "rightExpression", "isUpdateExpression", "updateExpr", "computedKey", "parts", "prefix", "superClass", "assignSuperThisVisitor", "supers", "has", "add", "replaceWithMultiple", "WeakSet", "args<PERSON><PERSON><PERSON>", "propName", "argsList", "fnBody", "method", "unshift", "valueIdent", "cache<PERSON>ey", "data", "getData", "setData", "getScopeInformationVisitor", "ThisExpression", "JSXIdentifier", "isJSXMemberExpression", "isJSXOpeningElement", "MemberExpression", "Identifier", "isReferencedIdentifier", "curr", "hasOwnBinding", "rename", "parent", "MetaProperty", "splitExportDeclaration", "isExportDeclaration", "isExportAllDeclaration", "isExportNamedDeclaration", "declaration", "isExportDefaultDeclaration", "standaloneDeclaration", "isClassDeclaration", "exportExpr", "isClassExpression", "isScope", "needBindingRegistration", "hasBinding", "updatedDeclaration", "updatedExportDeclaration", "insertAfter", "registerDeclaration", "bindingIdentifiers", "getOuterBindingIdentifiers", "specifiers", "Object", "keys", "map", "aliasDeclar", "refersOuterBindingVisitor", "ReferencedIdentifier|BindingIdentifier", "state", "<PERSON><PERSON><PERSON><PERSON>", "stop", "<PERSON><PERSON>", "skip", "supportUnicodeId", "res", "test", "startsWith", "replace", "originalNode", "binding", "getOwnBinding", "hasGlobal", "getProgramParent", "references", "params", "i", "len", "getFunctionArity", "template", "expression", "ast", "count", "findIndex", "param"], "sources": ["../../src/path/conversion.ts"], "sourcesContent": ["// This file contains methods that convert the path node into another node or some other type of data.\n\nimport {\n  arrowFunctionExpression,\n  assignmentExpression,\n  binaryExpression,\n  blockStatement,\n  callExpression,\n  conditionalExpression,\n  expressionStatement,\n  identifier,\n  isIdentifier,\n  jsxIdentifier,\n  logicalExpression,\n  LOGICAL_OPERATORS,\n  memberExpression,\n  metaProperty,\n  numericLiteral,\n  objectExpression,\n  restElement,\n  returnStatement,\n  sequenceExpression,\n  spreadElement,\n  stringLiteral,\n  super as _super,\n  thisExpression,\n  toExpression,\n  unaryExpression,\n  toBindingIdentifierName,\n  isFunction,\n  isAssignmentPattern,\n  isRestElement,\n  getFunctionName,\n  cloneNode,\n  variableDeclaration,\n  variableDeclarator,\n  exportNamedDeclaration,\n  exportSpecifier,\n  inherits,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport template from \"@babel/template\";\nimport { environmentVisitor } from \"../visitors.ts\";\nimport type NodePath from \"./index.ts\";\nimport type { Visitor } from \"../types.ts\";\nimport { setup } from \"./context.ts\";\n\nexport function toComputedKey(this: NodePath) {\n  let key;\n  if (this.isMemberExpression()) {\n    key = this.node.property;\n  } else if (this.isProperty() || this.isMethod()) {\n    key = this.node.key;\n  } else {\n    throw new ReferenceError(\"todo\");\n  }\n\n  // @ts-expect-error todo(flow->ts) computed does not exist in ClassPrivateProperty\n  if (!this.node.computed) {\n    if (isIdentifier(key)) key = stringLiteral(key.name);\n  }\n\n  return key;\n}\n\nexport function ensureBlock(\n  this: NodePath<\n    t.Loop | t.WithStatement | t.Function | t.LabeledStatement | t.CatchClause\n  >,\n): void {\n  const body = this.get(\"body\");\n  const bodyNode = body.node;\n\n  if (Array.isArray(body)) {\n    throw new Error(\"Can't convert array path to a block statement\");\n  }\n  if (!bodyNode) {\n    throw new Error(\"Can't convert node without a body\");\n  }\n\n  if (body.isBlockStatement()) {\n    // @ts-expect-error TS throws because ensureBlock returns the body node path\n    // however, we don't use the return value and treat it as a transform and\n    // assertion utilities. For better type inference we annotate it as an\n    // assertion method\n    // TODO: Unify the implementation with the type definition\n    return bodyNode;\n  }\n\n  const statements: Array<t.Statement> = [];\n\n  let stringPath = \"body\";\n  let key;\n  let listKey;\n  if (body.isStatement()) {\n    listKey = \"body\";\n    key = 0;\n    statements.push(body.node);\n  } else {\n    stringPath += \".body.0\";\n    if (this.isFunction()) {\n      key = \"argument\";\n      statements.push(returnStatement(body.node as t.Expression));\n    } else {\n      key = \"expression\";\n      statements.push(expressionStatement(body.node as t.Expression));\n    }\n  }\n\n  this.node.body = blockStatement(statements);\n  const parentPath = this.get(stringPath) as NodePath;\n  setup.call(\n    body,\n    parentPath,\n    listKey\n      ? // @ts-expect-error listKey must present in parent path\n        parentPath.node[listKey]\n      : parentPath.node,\n    listKey,\n    key,\n  );\n\n  // @ts-expect-error TS throws because ensureBlock returns the body node path\n  // however, we don't use the return value and treat it as a transform and\n  // assertion utilities. For better type inference we annotate it as an\n  // assertion method\n  // TODO: Unify the implementation with the type definition\n  return this.node;\n}\n\nif (!process.env.BABEL_8_BREAKING && !USE_ESM) {\n  /**\n   * Keeping this for backward-compatibility. You should use arrowFunctionToExpression() for >=7.x.\n   */\n  // eslint-disable-next-line no-restricted-globals\n  exports.arrowFunctionToShadowed = function (this: NodePath) {\n    if (!this.isArrowFunctionExpression()) return;\n\n    this.arrowFunctionToExpression();\n  };\n}\n\n/**\n * Given an arbitrary function, process its content as if it were an arrow function, moving references\n * to \"this\", \"arguments\", \"super\", and such into the function's parent scope. This method is useful if\n * you have wrapped some set of items in an IIFE or other function, but want \"this\", \"arguments\", and super\"\n * to continue behaving as expected.\n */\nexport function unwrapFunctionEnvironment(this: NodePath) {\n  if (\n    !this.isArrowFunctionExpression() &&\n    !this.isFunctionExpression() &&\n    !this.isFunctionDeclaration()\n  ) {\n    throw this.buildCodeFrameError(\n      \"Can only unwrap the environment of a function.\",\n    );\n  }\n\n  hoistFunctionEnvironment(this);\n}\n\nfunction setType<N extends t.Node, T extends N[\"type\"]>(\n  path: NodePath<N>,\n  type: T,\n): asserts path is NodePath<Extract<N, { type: T }>> {\n  path.node.type = type;\n}\n\n/**\n * Convert a given arrow function into a normal ES5 function expression.\n */\nexport function arrowFunctionToExpression(\n  this: NodePath<t.ArrowFunctionExpression>,\n  {\n    allowInsertArrow = true,\n    allowInsertArrowWithRest = allowInsertArrow,\n    noNewArrows = process.env.BABEL_8_BREAKING\n      ? // TODO(Babel 8): Consider defaulting to `false` for spec compliance\n        true\n      : !arguments[0]?.specCompliant,\n  }: {\n    allowInsertArrow?: boolean | void;\n    allowInsertArrowWithRest?: boolean | void;\n    noNewArrows?: boolean;\n  } = {},\n): NodePath<\n  Exclude<t.Function, t.Method | t.ArrowFunctionExpression> | t.CallExpression\n> {\n  if (!this.isArrowFunctionExpression()) {\n    throw (this as NodePath).buildCodeFrameError(\n      \"Cannot convert non-arrow function to a function expression.\",\n    );\n  }\n\n  let self = this;\n  if (!noNewArrows) {\n    // @ts-expect-error This is technicallynot valid on arrow functions\n    // because it adds an .id property, but we are going to convert it\n    // to a function expression anyway\n    self = self.ensureFunctionName(false) ?? self;\n  }\n\n  const { thisBinding, fnPath: fn } = hoistFunctionEnvironment(\n    self,\n    noNewArrows,\n    allowInsertArrow,\n    allowInsertArrowWithRest,\n  );\n\n  fn.ensureBlock();\n  setType(fn, \"FunctionExpression\");\n\n  if (!noNewArrows) {\n    const checkBinding = thisBinding\n      ? null\n      : fn.scope.generateUidIdentifier(\"arrowCheckId\");\n    if (checkBinding) {\n      fn.parentPath.scope.push({\n        id: checkBinding,\n        init: objectExpression([]),\n      });\n    }\n\n    fn.get(\"body\").unshiftContainer(\n      \"body\",\n      expressionStatement(\n        callExpression(this.hub.addHelper(\"newArrowCheck\"), [\n          thisExpression(),\n          checkBinding\n            ? identifier(checkBinding.name)\n            : identifier(thisBinding),\n        ]),\n      ),\n    );\n\n    fn.replaceWith(\n      callExpression(memberExpression(fn.node, identifier(\"bind\")), [\n        checkBinding ? identifier(checkBinding.name) : thisExpression(),\n      ]),\n    );\n\n    return fn.get(\"callee.object\");\n  }\n\n  return fn;\n}\n\nconst getSuperCallsVisitor = environmentVisitor<{\n  allSuperCalls: NodePath<t.CallExpression>[];\n}>({\n  CallExpression(child, { allSuperCalls }) {\n    if (!child.get(\"callee\").isSuper()) return;\n    allSuperCalls.push(child);\n  },\n});\n\n/**\n * Given a function, traverse its contents, and if there are references to \"this\", \"arguments\", \"super\",\n * or \"new.target\", ensure that these references reference the parent environment around this function.\n *\n * @returns `thisBinding`: the name of the injected reference to `this`; for example \"_this\"\n * @returns `fnPath`: the new path to the function node. This is different from the fnPath\n *                    parameter when the function node is wrapped in another node.\n */\nfunction hoistFunctionEnvironment(\n  fnPath: NodePath<t.Function>,\n  // TODO(Babel 8): Consider defaulting to `false` for spec compliance\n  noNewArrows: boolean | void = true,\n  allowInsertArrow: boolean | void = true,\n  allowInsertArrowWithRest: boolean | void = true,\n): { thisBinding: string; fnPath: NodePath<t.Function> } {\n  let arrowParent;\n  let thisEnvFn: NodePath<t.Function> = fnPath.findParent(p => {\n    if (p.isArrowFunctionExpression()) {\n      arrowParent ??= p;\n      return false;\n    }\n    return (\n      p.isFunction() ||\n      p.isProgram() ||\n      p.isClassProperty({ static: false }) ||\n      p.isClassPrivateProperty({ static: false })\n    );\n  }) as NodePath<t.Function>;\n  const inConstructor = thisEnvFn.isClassMethod({ kind: \"constructor\" });\n\n  if (thisEnvFn.isClassProperty() || thisEnvFn.isClassPrivateProperty()) {\n    if (arrowParent) {\n      thisEnvFn = arrowParent;\n    } else if (allowInsertArrow) {\n      // It's safe to wrap this function in another and not hoist to the\n      // top level because the 'this' binding is constant in class\n      // properties (since 'super()' has already been called), so we don't\n      // need to capture/reassign it at the top level.\n      fnPath.replaceWith(\n        callExpression(\n          arrowFunctionExpression([], toExpression(fnPath.node)),\n          [],\n        ),\n      );\n      thisEnvFn = fnPath.get(\"callee\") as NodePath<t.ArrowFunctionExpression>;\n      fnPath = thisEnvFn.get(\"body\") as NodePath<t.FunctionExpression>;\n    } else {\n      throw fnPath.buildCodeFrameError(\n        \"Unable to transform arrow inside class property\",\n      );\n    }\n  }\n\n  const { thisPaths, argumentsPaths, newTargetPaths, superProps, superCalls } =\n    getScopeInformation(fnPath);\n\n  // Convert all super() calls in the constructor, if super is used in an arrow.\n  if (inConstructor && superCalls.length > 0) {\n    if (!allowInsertArrow) {\n      throw superCalls[0].buildCodeFrameError(\n        \"When using '@babel/plugin-transform-arrow-functions', \" +\n          \"it's not possible to compile `super()` in an arrow function without compiling classes.\\n\" +\n          \"Please add '@babel/plugin-transform-classes' to your Babel configuration.\",\n      );\n    }\n    if (!allowInsertArrowWithRest) {\n      // preset-env with target `since 2017` enables `transform-parameters` without `transform-classes`.\n      throw superCalls[0].buildCodeFrameError(\n        \"When using '@babel/plugin-transform-parameters', \" +\n          \"it's not possible to compile `super()` in an arrow function with default or rest parameters without compiling classes.\\n\" +\n          \"Please add '@babel/plugin-transform-classes' to your Babel configuration.\",\n      );\n    }\n    const allSuperCalls: NodePath<t.CallExpression>[] = [];\n    thisEnvFn.traverse(getSuperCallsVisitor, { allSuperCalls });\n    const superBinding = getSuperBinding(thisEnvFn);\n    allSuperCalls.forEach(superCall => {\n      const callee = identifier(superBinding);\n      callee.loc = superCall.node.callee.loc;\n\n      superCall.get(\"callee\").replaceWith(callee);\n    });\n  }\n\n  // Convert all \"arguments\" references in the arrow to point at the alias.\n  if (argumentsPaths.length > 0) {\n    const argumentsBinding = getBinding(thisEnvFn, \"arguments\", () => {\n      const args = () => identifier(\"arguments\");\n      if (thisEnvFn.scope.path.isProgram()) {\n        return conditionalExpression(\n          binaryExpression(\n            \"===\",\n            unaryExpression(\"typeof\", args()),\n            stringLiteral(\"undefined\"),\n          ),\n          thisEnvFn.scope.buildUndefinedNode(),\n          args(),\n        );\n      } else {\n        return args();\n      }\n    });\n\n    argumentsPaths.forEach(argumentsChild => {\n      const argsRef = identifier(argumentsBinding);\n      argsRef.loc = argumentsChild.node.loc;\n\n      argumentsChild.replaceWith(argsRef);\n    });\n  }\n\n  // Convert all \"new.target\" references in the arrow to point at the alias.\n  if (newTargetPaths.length > 0) {\n    const newTargetBinding = getBinding(thisEnvFn, \"newtarget\", () =>\n      metaProperty(identifier(\"new\"), identifier(\"target\")),\n    );\n\n    newTargetPaths.forEach(targetChild => {\n      const targetRef = identifier(newTargetBinding);\n      targetRef.loc = targetChild.node.loc;\n\n      targetChild.replaceWith(targetRef);\n    });\n  }\n\n  // Convert all \"super.prop\" references to point at aliases.\n  if (superProps.length > 0) {\n    if (!allowInsertArrow) {\n      throw superProps[0].buildCodeFrameError(\n        \"When using '@babel/plugin-transform-arrow-functions', \" +\n          \"it's not possible to compile `super.prop` in an arrow function without compiling classes.\\n\" +\n          \"Please add '@babel/plugin-transform-classes' to your Babel configuration.\",\n      );\n    }\n\n    const flatSuperProps: NodePath<t.MemberExpression>[] = superProps.reduce(\n      (acc, superProp) => acc.concat(standardizeSuperProperty(superProp)),\n      [],\n    );\n\n    flatSuperProps.forEach(superProp => {\n      const key = superProp.node.computed\n        ? \"\"\n        : // @ts-expect-error super property must not contain private name\n          superProp.get(\"property\").node.name;\n\n      const superParentPath = superProp.parentPath;\n\n      const isAssignment = superParentPath.isAssignmentExpression({\n        left: superProp.node,\n      });\n      const isCall = superParentPath.isCallExpression({\n        callee: superProp.node,\n      });\n      const isTaggedTemplate = superParentPath.isTaggedTemplateExpression({\n        tag: superProp.node,\n      });\n      const superBinding = getSuperPropBinding(thisEnvFn, isAssignment, key);\n\n      const args: t.Expression[] = [];\n      if (superProp.node.computed) {\n        // SuperProperty must not be a private name\n        args.push(superProp.get(\"property\").node as t.Expression);\n      }\n\n      if (isAssignment) {\n        const value = superParentPath.node.right;\n        args.push(value);\n      }\n\n      const call = callExpression(identifier(superBinding), args);\n\n      if (isCall) {\n        superParentPath.unshiftContainer(\"arguments\", thisExpression());\n        superProp.replaceWith(memberExpression(call, identifier(\"call\")));\n\n        thisPaths.push(\n          superParentPath.get(\"arguments.0\") as NodePath<t.ThisExpression>,\n        );\n      } else if (isAssignment) {\n        // Replace not only the super.prop, but the whole assignment\n        superParentPath.replaceWith(call);\n      } else if (isTaggedTemplate) {\n        superProp.replaceWith(\n          callExpression(memberExpression(call, identifier(\"bind\"), false), [\n            thisExpression(),\n          ]),\n        );\n\n        thisPaths.push(\n          superProp.get(\"arguments.0\") as NodePath<t.ThisExpression>,\n        );\n      } else {\n        superProp.replaceWith(call);\n      }\n    });\n  }\n\n  // Convert all \"this\" references in the arrow to point at the alias.\n  let thisBinding: string | null;\n  if (thisPaths.length > 0 || !noNewArrows) {\n    thisBinding = getThisBinding(thisEnvFn, inConstructor);\n\n    if (\n      noNewArrows ||\n      // In subclass constructors, still need to rewrite because \"this\" can't be bound in spec mode\n      // because it might not have been initialized yet.\n      (inConstructor && hasSuperClass(thisEnvFn))\n    ) {\n      thisPaths.forEach(thisChild => {\n        const thisRef = thisChild.isJSX()\n          ? jsxIdentifier(thisBinding)\n          : identifier(thisBinding);\n\n        thisRef.loc = thisChild.node.loc;\n        thisChild.replaceWith(thisRef);\n      });\n\n      if (!noNewArrows) thisBinding = null;\n    }\n  }\n\n  return { thisBinding, fnPath };\n}\n\ntype LogicalOp = Parameters<typeof logicalExpression>[0];\ntype BinaryOp = Parameters<typeof binaryExpression>[0];\n\nfunction isLogicalOp(op: string): op is LogicalOp {\n  return LOGICAL_OPERATORS.includes(op);\n}\n\nfunction standardizeSuperProperty(\n  superProp: NodePath<t.MemberExpression>,\n):\n  | [NodePath<t.MemberExpression>]\n  | [NodePath<t.MemberExpression>, NodePath<t.MemberExpression>] {\n  if (\n    superProp.parentPath.isAssignmentExpression() &&\n    superProp.parentPath.node.operator !== \"=\"\n  ) {\n    const assignmentPath = superProp.parentPath;\n\n    const op = assignmentPath.node.operator.slice(0, -1) as\n      | LogicalOp\n      | BinaryOp;\n\n    const value = assignmentPath.node.right;\n\n    const isLogicalAssignment = isLogicalOp(op);\n\n    if (superProp.node.computed) {\n      // from: super[foo] **= 4;\n      // to:   super[tmp = foo] = super[tmp] ** 4;\n\n      // from: super[foo] ??= 4;\n      // to:   super[tmp = foo] ?? super[tmp] = 4;\n\n      const tmp = superProp.scope.generateDeclaredUidIdentifier(\"tmp\");\n\n      const object = superProp.node.object;\n      const property = superProp.node.property as t.Expression;\n\n      assignmentPath\n        .get(\"left\")\n        .replaceWith(\n          memberExpression(\n            object,\n            assignmentExpression(\"=\", tmp, property),\n            true /* computed */,\n          ),\n        );\n\n      assignmentPath\n        .get(\"right\")\n        .replaceWith(\n          rightExpression(\n            isLogicalAssignment ? \"=\" : op,\n            memberExpression(object, identifier(tmp.name), true /* computed */),\n            value,\n          ),\n        );\n    } else {\n      // from: super.foo **= 4;\n      // to:   super.foo = super.foo ** 4;\n\n      // from: super.foo ??= 4;\n      // to:   super.foo ?? super.foo = 4;\n\n      const object = superProp.node.object;\n      const property = superProp.node.property as t.Identifier;\n\n      assignmentPath\n        .get(\"left\")\n        .replaceWith(memberExpression(object, property));\n\n      assignmentPath\n        .get(\"right\")\n        .replaceWith(\n          rightExpression(\n            isLogicalAssignment ? \"=\" : op,\n            memberExpression(object, identifier(property.name)),\n            value,\n          ),\n        );\n    }\n\n    if (isLogicalAssignment) {\n      assignmentPath.replaceWith(\n        logicalExpression(\n          op,\n          assignmentPath.node.left as t.MemberExpression,\n          assignmentPath.node.right,\n        ),\n      );\n    } else {\n      assignmentPath.node.operator = \"=\";\n    }\n\n    return [\n      assignmentPath.get(\"left\") as NodePath<t.MemberExpression>,\n      assignmentPath.get(\"right\").get(\"left\") as NodePath<t.MemberExpression>,\n    ];\n  } else if (superProp.parentPath.isUpdateExpression()) {\n    const updateExpr = superProp.parentPath;\n\n    const tmp = superProp.scope.generateDeclaredUidIdentifier(\"tmp\");\n    const computedKey = superProp.node.computed\n      ? superProp.scope.generateDeclaredUidIdentifier(\"prop\")\n      : null;\n\n    const parts: t.Expression[] = [\n      assignmentExpression(\n        \"=\",\n        tmp,\n        memberExpression(\n          superProp.node.object,\n          computedKey\n            ? assignmentExpression(\n                \"=\",\n                computedKey,\n                superProp.node.property as t.Expression,\n              )\n            : superProp.node.property,\n          superProp.node.computed,\n        ),\n      ),\n      assignmentExpression(\n        \"=\",\n        memberExpression(\n          superProp.node.object,\n          computedKey ? identifier(computedKey.name) : superProp.node.property,\n          superProp.node.computed,\n        ),\n        binaryExpression(\n          // map `++` to `+`, and `--` to `-`\n          superProp.parentPath.node.operator[0] as \"+\" | \"-\",\n          identifier(tmp.name),\n          numericLiteral(1),\n        ),\n      ),\n    ];\n\n    if (!superProp.parentPath.node.prefix) {\n      parts.push(identifier(tmp.name));\n    }\n\n    updateExpr.replaceWith(sequenceExpression(parts));\n\n    const left = updateExpr.get(\n      \"expressions.0.right\",\n    ) as NodePath<t.MemberExpression>;\n    const right = updateExpr.get(\n      \"expressions.1.left\",\n    ) as NodePath<t.MemberExpression>;\n    return [left, right];\n  }\n\n  return [superProp];\n\n  function rightExpression(\n    op: BinaryOp | \"=\",\n    left: t.MemberExpression,\n    right: t.Expression,\n  ) {\n    if (op === \"=\") {\n      return assignmentExpression(\"=\", left, right);\n    } else {\n      return binaryExpression(op, left, right);\n    }\n  }\n}\n\nfunction hasSuperClass(thisEnvFn: NodePath<t.Function>) {\n  return (\n    thisEnvFn.isClassMethod() &&\n    !!(thisEnvFn.parentPath.parentPath.node as t.Class).superClass\n  );\n}\n\nconst assignSuperThisVisitor = environmentVisitor<{\n  supers: WeakSet<t.CallExpression>;\n  thisBinding: string;\n}>({\n  CallExpression(child, { supers, thisBinding }) {\n    if (!child.get(\"callee\").isSuper()) return;\n    if (supers.has(child.node)) return;\n    supers.add(child.node);\n\n    child.replaceWithMultiple([\n      child.node,\n      assignmentExpression(\"=\", identifier(thisBinding), identifier(\"this\")),\n    ]);\n  },\n});\n\n// Create a binding that evaluates to the \"this\" of the given function.\nfunction getThisBinding(\n  thisEnvFn: NodePath<t.Function>,\n  inConstructor: boolean,\n) {\n  return getBinding(thisEnvFn, \"this\", thisBinding => {\n    if (!inConstructor || !hasSuperClass(thisEnvFn)) return thisExpression();\n\n    thisEnvFn.traverse(assignSuperThisVisitor, {\n      supers: new WeakSet(),\n      thisBinding,\n    });\n  });\n}\n\n// Create a binding for a function that will call \"super()\" with arguments passed through.\nfunction getSuperBinding(thisEnvFn: NodePath<t.Function>) {\n  return getBinding(thisEnvFn, \"supercall\", () => {\n    const argsBinding = thisEnvFn.scope.generateUidIdentifier(\"args\");\n    return arrowFunctionExpression(\n      [restElement(argsBinding)],\n      callExpression(_super(), [spreadElement(identifier(argsBinding.name))]),\n    );\n  });\n}\n\n// Create a binding for a function that will call \"super.foo\" or \"super[foo]\".\nfunction getSuperPropBinding(\n  thisEnvFn: NodePath<t.Function>,\n  isAssignment: boolean,\n  propName: string,\n) {\n  const op = isAssignment ? \"set\" : \"get\";\n\n  return getBinding(thisEnvFn, `superprop_${op}:${propName || \"\"}`, () => {\n    const argsList = [];\n\n    let fnBody;\n    if (propName) {\n      // () => super.foo\n      fnBody = memberExpression(_super(), identifier(propName));\n    } else {\n      const method = thisEnvFn.scope.generateUidIdentifier(\"prop\");\n      // (method) => super[method]\n      argsList.unshift(method);\n      fnBody = memberExpression(\n        _super(),\n        identifier(method.name),\n        true /* computed */,\n      );\n    }\n\n    if (isAssignment) {\n      const valueIdent = thisEnvFn.scope.generateUidIdentifier(\"value\");\n      argsList.push(valueIdent);\n\n      fnBody = assignmentExpression(\"=\", fnBody, identifier(valueIdent.name));\n    }\n\n    return arrowFunctionExpression(argsList, fnBody);\n  });\n}\n\nfunction getBinding(\n  thisEnvFn: NodePath,\n  key: string,\n  init: (name: string) => t.Expression,\n) {\n  const cacheKey = \"binding:\" + key;\n  let data: string | undefined = thisEnvFn.getData(cacheKey);\n  if (!data) {\n    const id = thisEnvFn.scope.generateUidIdentifier(key);\n    data = id.name;\n    thisEnvFn.setData(cacheKey, data);\n\n    thisEnvFn.scope.push({\n      id: id,\n      init: init(data),\n    });\n  }\n\n  return data;\n}\n\ntype ScopeInfo = {\n  thisPaths: NodePath<t.ThisExpression | t.JSXIdentifier>[];\n  superCalls: NodePath<t.CallExpression>[];\n  superProps: NodePath<t.MemberExpression>[];\n  argumentsPaths: NodePath<t.Identifier | t.JSXIdentifier>[];\n  newTargetPaths: NodePath<t.MetaProperty>[];\n};\n\nconst getScopeInformationVisitor = environmentVisitor<ScopeInfo>({\n  ThisExpression(child, { thisPaths }) {\n    thisPaths.push(child);\n  },\n  JSXIdentifier(child, { thisPaths }) {\n    if (child.node.name !== \"this\") return;\n    if (\n      !child.parentPath.isJSXMemberExpression({ object: child.node }) &&\n      !child.parentPath.isJSXOpeningElement({ name: child.node })\n    ) {\n      return;\n    }\n\n    thisPaths.push(child);\n  },\n  CallExpression(child, { superCalls }) {\n    if (child.get(\"callee\").isSuper()) superCalls.push(child);\n  },\n  MemberExpression(child, { superProps }) {\n    if (child.get(\"object\").isSuper()) superProps.push(child);\n  },\n  Identifier(child, { argumentsPaths }) {\n    if (!child.isReferencedIdentifier({ name: \"arguments\" })) return;\n\n    let curr = child.scope;\n    do {\n      if (curr.hasOwnBinding(\"arguments\")) {\n        curr.rename(\"arguments\");\n        return;\n      }\n      if (curr.path.isFunction() && !curr.path.isArrowFunctionExpression()) {\n        break;\n      }\n    } while ((curr = curr.parent));\n\n    argumentsPaths.push(child);\n  },\n  MetaProperty(child, { newTargetPaths }) {\n    if (!child.get(\"meta\").isIdentifier({ name: \"new\" })) return;\n    if (!child.get(\"property\").isIdentifier({ name: \"target\" })) return;\n\n    newTargetPaths.push(child);\n  },\n});\n\nfunction getScopeInformation(fnPath: NodePath) {\n  const thisPaths: ScopeInfo[\"thisPaths\"] = [];\n  const argumentsPaths: ScopeInfo[\"argumentsPaths\"] = [];\n  const newTargetPaths: ScopeInfo[\"newTargetPaths\"] = [];\n  const superProps: ScopeInfo[\"superProps\"] = [];\n  const superCalls: ScopeInfo[\"superCalls\"] = [];\n\n  fnPath.traverse(getScopeInformationVisitor, {\n    thisPaths,\n    argumentsPaths,\n    newTargetPaths,\n    superProps,\n    superCalls,\n  });\n\n  return {\n    thisPaths,\n    argumentsPaths,\n    newTargetPaths,\n    superProps,\n    superCalls,\n  };\n}\n\nexport function splitExportDeclaration(\n  this: NodePath<t.ExportDefaultDeclaration | t.ExportNamedDeclaration>,\n): NodePath<t.Declaration> {\n  if (!this.isExportDeclaration() || this.isExportAllDeclaration()) {\n    throw new Error(\"Only default and named export declarations can be split.\");\n  }\n  if (this.isExportNamedDeclaration() && this.get(\"specifiers\").length > 0) {\n    throw new Error(\"It doesn't make sense to split exported specifiers.\");\n  }\n\n  const declaration = this.get(\"declaration\");\n\n  if (this.isExportDefaultDeclaration()) {\n    const standaloneDeclaration =\n      declaration.isFunctionDeclaration() || declaration.isClassDeclaration();\n    const exportExpr =\n      declaration.isFunctionExpression() || declaration.isClassExpression();\n\n    const scope = declaration.isScope()\n      ? declaration.scope.parent\n      : declaration.scope;\n\n    // @ts-expect-error id is not defined in expressions other than function/class\n    let id = declaration.node.id;\n    let needBindingRegistration = false;\n\n    if (!id) {\n      needBindingRegistration = true;\n\n      id = scope.generateUidIdentifier(\"default\");\n\n      if (standaloneDeclaration || exportExpr) {\n        declaration.node.id = cloneNode(id);\n      }\n    } else if (exportExpr && scope.hasBinding(id.name)) {\n      needBindingRegistration = true;\n\n      id = scope.generateUidIdentifier(id.name);\n    }\n\n    const updatedDeclaration = standaloneDeclaration\n      ? declaration.node\n      : variableDeclaration(\"var\", [\n          variableDeclarator(\n            cloneNode(id),\n            // @ts-expect-error When `standaloneDeclaration` is false, declaration must not be a Function/ClassDeclaration\n            declaration.node,\n          ),\n        ]);\n\n    const updatedExportDeclaration = exportNamedDeclaration(null, [\n      exportSpecifier(cloneNode(id), identifier(\"default\")),\n    ]);\n\n    this.insertAfter(updatedExportDeclaration);\n    this.replaceWith(updatedDeclaration);\n\n    if (needBindingRegistration) {\n      scope.registerDeclaration(this);\n    }\n\n    return this;\n  } else if (this.get(\"specifiers\").length > 0) {\n    throw new Error(\"It doesn't make sense to split exported specifiers.\");\n  }\n\n  const bindingIdentifiers = declaration.getOuterBindingIdentifiers();\n\n  const specifiers = Object.keys(bindingIdentifiers).map(name => {\n    return exportSpecifier(identifier(name), identifier(name));\n  });\n\n  const aliasDeclar = exportNamedDeclaration(null, specifiers);\n\n  this.insertAfter(aliasDeclar);\n  this.replaceWith(declaration.node);\n  return this;\n}\n\nconst refersOuterBindingVisitor: Visitor<{\n  needsRename: boolean;\n  name: string;\n}> = {\n  \"ReferencedIdentifier|BindingIdentifier\"(\n    path: NodePath<t.Identifier>,\n    state,\n  ) {\n    // check if this node matches our function id\n    if (path.node.name !== state.name) return;\n    state.needsRename = true;\n    path.stop();\n  },\n  Scope(path, state) {\n    if (path.scope.hasOwnBinding(state.name)) {\n      path.skip();\n    }\n  },\n};\n\nexport function ensureFunctionName<\n  N extends t.FunctionExpression | t.ClassExpression,\n>(this: NodePath<N>, supportUnicodeId: boolean): null | NodePath<N> {\n  if (this.node.id) return this;\n\n  const res = getFunctionName(this.node, this.parent);\n  if (res == null) return this;\n  let { name } = res;\n\n  if (!supportUnicodeId && /[\\uD800-\\uDFFF]/.test(name)) {\n    return null;\n  }\n\n  if (name.startsWith(\"get \") || name.startsWith(\"set \")) {\n    // TODO: Remove this to support naming getters and setters\n    return null;\n  }\n\n  name = toBindingIdentifierName(name.replace(/[/ ]/g, \"_\"));\n  const id = identifier(name);\n  inherits(id, res.originalNode);\n\n  const state = { needsRename: false, name };\n\n  // check to see if we have a local binding of the id we're setting inside of\n  // the function, this is important as there are caveats associated\n\n  const { scope } = this;\n  const binding = scope.getOwnBinding(name);\n  if (binding) {\n    if (binding.kind === \"param\") {\n      // safari will blow up in strict mode with code like:\n      //\n      //   let t = function t(t) {};\n      //\n      // with the error:\n      //\n      //   Cannot declare a parameter named 't' as it shadows the name of a\n      //   strict mode function.\n      //\n      // this isn't to the spec and they've invented this behaviour which is\n      // **extremely** annoying so we avoid setting the name if it has a param\n      // with the same id\n      state.needsRename = true;\n    } else {\n      // otherwise it's defined somewhere in scope like:\n      //\n      //   let t = function () {\n      //     let t = 2;\n      //   };\n      //\n      // so we can safely just set the id and move along as it shadows the\n      // bound function id\n    }\n  } else if (scope.parent.hasBinding(name) || scope.hasGlobal(name)) {\n    this.traverse(refersOuterBindingVisitor, state);\n  }\n\n  if (!state.needsRename) {\n    this.node.id = id;\n    if (process.env.BABEL_8_BREAKING) {\n      scope.getProgramParent().referencesSet.add(id.name);\n    } else {\n      // @ts-expect-error Babel 7\n      scope.getProgramParent().references[id.name] = true;\n    }\n    return this;\n  }\n\n  if (scope.hasBinding(id.name) && !scope.hasGlobal(id.name)) {\n    // we can just munge the local binding\n    scope.rename(id.name);\n    this.node.id = id;\n    if (process.env.BABEL_8_BREAKING) {\n      scope.getProgramParent().referencesSet.add(id.name);\n    } else {\n      // @ts-expect-error Babel 7\n      scope.getProgramParent().references[id.name] = true;\n    }\n    return this;\n  }\n\n  // TODO: we don't currently support wrapping class expressions\n  if (!isFunction(this.node)) return null;\n\n  // need to add a wrapper since we can't change the references\n\n  const key = scope.generateUidIdentifier(id.name);\n  // shim in dummy params to retain function arity, if you try to read the\n  // source then you'll get the original since it's proxied so it's all good\n  const params = [];\n  for (let i = 0, len = getFunctionArity(this.node); i < len; i++) {\n    params.push(scope.generateUidIdentifier(\"x\"));\n  }\n  const call = template.expression.ast`\n    (function (${key}) {\n      function ${id}(${params}) {\n        return ${cloneNode(key)}.apply(this, arguments);\n      }\n\n      ${cloneNode(id)}.toString = function () {\n        return ${cloneNode(key)}.toString();\n      }\n\n      return ${cloneNode(id)};\n    })(${toExpression(this.node)})\n  ` as t.CallExpression;\n\n  return this.replaceWith(call)[0].get(\"arguments.0\") as NodePath<N>;\n}\n\nfunction getFunctionArity(node: t.Function): number {\n  const count = node.params.findIndex(\n    param => isAssignmentPattern(param) || isRestElement(param),\n  );\n  return count === -1 ? node.params.length : count;\n}\n"], "mappings": ";;;;;;;;;;;AAEA,IAAAA,EAAA,GAAAC,OAAA;AAuCA,IAAAC,SAAA,GAAAD,OAAA;AACA,IAAAE,SAAA,GAAAF,OAAA;AAGA,IAAAG,QAAA,GAAAH,OAAA;AAAqC;EA1CnCI,uBAAuB;EACvBC,oBAAoB;EACpBC,gBAAgB;EAChBC,cAAc;EACdC,cAAc;EACdC,qBAAqB;EACrBC,mBAAmB;EACnBC,UAAU;EACVC,YAAY;EACZC,aAAa;EACbC,iBAAiB;EACjBC,iBAAiB;EACjBC,gBAAgB;EAChBC,YAAY;EACZC,cAAc;EACdC,gBAAgB;EAChBC,WAAW;EACXC,eAAe;EACfC,kBAAkB;EAClBC,aAAa;EACbC,aAAa;EACbC,KAAK,EAAIC,MAAM;EACfC,cAAc;EACdC,YAAY;EACZC,eAAe;EACfC,uBAAuB;EACvBC,UAAU;EACVC,mBAAmB;EACnBC,aAAa;EACbC,eAAe;EACfC,SAAS;EACTC,mBAAmB;EACnBC,kBAAkB;EAClBC,sBAAsB;EACtBC,eAAe;EACfC;AAAQ,IAAAzC,EAAA;AASH,SAAS0C,aAAaA,CAAA,EAAiB;EAC5C,IAAIC,GAAG;EACP,IAAI,IAAI,CAACC,kBAAkB,CAAC,CAAC,EAAE;IAC7BD,GAAG,GAAG,IAAI,CAACE,IAAI,CAACC,QAAQ;EAC1B,CAAC,MAAM,IAAI,IAAI,CAACC,UAAU,CAAC,CAAC,IAAI,IAAI,CAACC,QAAQ,CAAC,CAAC,EAAE;IAC/CL,GAAG,GAAG,IAAI,CAACE,IAAI,CAACF,GAAG;EACrB,CAAC,MAAM;IACL,MAAM,IAAIM,cAAc,CAAC,MAAM,CAAC;EAClC;EAGA,IAAI,CAAC,IAAI,CAACJ,IAAI,CAACK,QAAQ,EAAE;IACvB,IAAIrC,YAAY,CAAC8B,GAAG,CAAC,EAAEA,GAAG,GAAGlB,aAAa,CAACkB,GAAG,CAACQ,IAAI,CAAC;EACtD;EAEA,OAAOR,GAAG;AACZ;AAEO,SAASS,WAAWA,CAAA,EAInB;EACN,MAAMC,IAAI,GAAG,IAAI,CAACC,GAAG,CAAC,MAAM,CAAC;EAC7B,MAAMC,QAAQ,GAAGF,IAAI,CAACR,IAAI;EAE1B,IAAIW,KAAK,CAACC,OAAO,CAACJ,IAAI,CAAC,EAAE;IACvB,MAAM,IAAIK,KAAK,CAAC,+CAA+C,CAAC;EAClE;EACA,IAAI,CAACH,QAAQ,EAAE;IACb,MAAM,IAAIG,KAAK,CAAC,mCAAmC,CAAC;EACtD;EAEA,IAAIL,IAAI,CAACM,gBAAgB,CAAC,CAAC,EAAE;IAM3B,OAAOJ,QAAQ;EACjB;EAEA,MAAMK,UAA8B,GAAG,EAAE;EAEzC,IAAIC,UAAU,GAAG,MAAM;EACvB,IAAIlB,GAAG;EACP,IAAImB,OAAO;EACX,IAAIT,IAAI,CAACU,WAAW,CAAC,CAAC,EAAE;IACtBD,OAAO,GAAG,MAAM;IAChBnB,GAAG,GAAG,CAAC;IACPiB,UAAU,CAACI,IAAI,CAACX,IAAI,CAACR,IAAI,CAAC;EAC5B,CAAC,MAAM;IACLgB,UAAU,IAAI,SAAS;IACvB,IAAI,IAAI,CAAC7B,UAAU,CAAC,CAAC,EAAE;MACrBW,GAAG,GAAG,UAAU;MAChBiB,UAAU,CAACI,IAAI,CAAC1C,eAAe,CAAC+B,IAAI,CAACR,IAAoB,CAAC,CAAC;IAC7D,CAAC,MAAM;MACLF,GAAG,GAAG,YAAY;MAClBiB,UAAU,CAACI,IAAI,CAACrD,mBAAmB,CAAC0C,IAAI,CAACR,IAAoB,CAAC,CAAC;IACjE;EACF;EAEA,IAAI,CAACA,IAAI,CAACQ,IAAI,GAAG7C,cAAc,CAACoD,UAAU,CAAC;EAC3C,MAAMK,UAAU,GAAG,IAAI,CAACX,GAAG,CAACO,UAAU,CAAa;EACnDK,cAAK,CAACC,IAAI,CACRd,IAAI,EACJY,UAAU,EACVH,OAAO,GAEHG,UAAU,CAACpB,IAAI,CAACiB,OAAO,CAAC,GACxBG,UAAU,CAACpB,IAAI,EACnBiB,OAAO,EACPnB,GACF,CAAC;EAOD,OAAO,IAAI,CAACE,IAAI;AAClB;AAE+C;EAK7CuB,OAAO,CAACC,uBAAuB,GAAG,YAA0B;IAC1D,IAAI,CAAC,IAAI,CAACC,yBAAyB,CAAC,CAAC,EAAE;IAEvC,IAAI,CAACC,yBAAyB,CAAC,CAAC;EAClC,CAAC;AACH;AAQO,SAASC,yBAAyBA,CAAA,EAAiB;EACxD,IACE,CAAC,IAAI,CAACF,yBAAyB,CAAC,CAAC,IACjC,CAAC,IAAI,CAACG,oBAAoB,CAAC,CAAC,IAC5B,CAAC,IAAI,CAACC,qBAAqB,CAAC,CAAC,EAC7B;IACA,MAAM,IAAI,CAACC,mBAAmB,CAC5B,gDACF,CAAC;EACH;EAEAC,wBAAwB,CAAC,IAAI,CAAC;AAChC;AAEA,SAASC,OAAOA,CACdC,IAAiB,EACjBC,IAAO,EAC4C;EACnDD,IAAI,CAACjC,IAAI,CAACkC,IAAI,GAAGA,IAAI;AACvB;AAKO,SAASR,yBAAyBA,CAEvC;EACES,gBAAgB,GAAG,IAAI;EACvBC,wBAAwB,GAAGD,gBAAgB;EAC3CE,WAAW,GAGP,EAAAC,WAAA,KAAAA,WAAA,GAACC,SAAS,CAAC,CAAC,CAAC,qBAAZD,WAAA,CAAcE,aAAa;AAKlC,CAAC,GAAG,CAAC,CAAC,EAGN;EACA,IAAI,CAAC,IAAI,CAACf,yBAAyB,CAAC,CAAC,EAAE;IACrC,MAAO,IAAI,CAAcK,mBAAmB,CAC1C,6DACF,CAAC;EACH;EAEA,IAAIW,IAAI,GAAG,IAAI;EACf,IAAI,CAACJ,WAAW,EAAE;IAAA,IAAAK,qBAAA;IAIhBD,IAAI,IAAAC,qBAAA,GAAGD,IAAI,CAACE,kBAAkB,CAAC,KAAK,CAAC,YAAAD,qBAAA,GAAID,IAAI;EAC/C;EAEA,MAAM;IAAEG,WAAW;IAAEC,MAAM,EAAEC;EAAG,CAAC,GAAGf,wBAAwB,CAC1DU,IAAI,EACJJ,WAAW,EACXF,gBAAgB,EAChBC,wBACF,CAAC;EAEDU,EAAE,CAACvC,WAAW,CAAC,CAAC;EAChByB,OAAO,CAACc,EAAE,EAAE,oBAAoB,CAAC;EAEjC,IAAI,CAACT,WAAW,EAAE;IAChB,MAAMU,YAAY,GAAGH,WAAW,GAC5B,IAAI,GACJE,EAAE,CAACE,KAAK,CAACC,qBAAqB,CAAC,cAAc,CAAC;IAClD,IAAIF,YAAY,EAAE;MAChBD,EAAE,CAAC1B,UAAU,CAAC4B,KAAK,CAAC7B,IAAI,CAAC;QACvB+B,EAAE,EAAEH,YAAY;QAChBI,IAAI,EAAE5E,gBAAgB,CAAC,EAAE;MAC3B,CAAC,CAAC;IACJ;IAEAuE,EAAE,CAACrC,GAAG,CAAC,MAAM,CAAC,CAAC2C,gBAAgB,CAC7B,MAAM,EACNtF,mBAAmB,CACjBF,cAAc,CAAC,IAAI,CAACyF,GAAG,CAACC,SAAS,CAAC,eAAe,CAAC,EAAE,CAClDvE,cAAc,CAAC,CAAC,EAChBgE,YAAY,GACRhF,UAAU,CAACgF,YAAY,CAACzC,IAAI,CAAC,GAC7BvC,UAAU,CAAC6E,WAAW,CAAC,CAC5B,CACH,CACF,CAAC;IAEDE,EAAE,CAACS,WAAW,CACZ3F,cAAc,CAACQ,gBAAgB,CAAC0E,EAAE,CAAC9C,IAAI,EAAEjC,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,CAC5DgF,YAAY,GAAGhF,UAAU,CAACgF,YAAY,CAACzC,IAAI,CAAC,GAAGvB,cAAc,CAAC,CAAC,CAChE,CACH,CAAC;IAED,OAAO+D,EAAE,CAACrC,GAAG,CAAC,eAAe,CAAC;EAChC;EAEA,OAAOqC,EAAE;AACX;AAEA,MAAMU,oBAAoB,GAAG,IAAAC,4BAAkB,EAE5C;EACDC,cAAcA,CAACC,KAAK,EAAE;IAAEC;EAAc,CAAC,EAAE;IACvC,IAAI,CAACD,KAAK,CAAClD,GAAG,CAAC,QAAQ,CAAC,CAACoD,OAAO,CAAC,CAAC,EAAE;IACpCD,aAAa,CAACzC,IAAI,CAACwC,KAAK,CAAC;EAC3B;AACF,CAAC,CAAC;AAUF,SAAS5B,wBAAwBA,CAC/Bc,MAA4B,EAE5BR,WAA2B,GAAG,IAAI,EAClCF,gBAAgC,GAAG,IAAI,EACvCC,wBAAwC,GAAG,IAAI,EACQ;EACvD,IAAI0B,WAAW;EACf,IAAIC,SAA+B,GAAGlB,MAAM,CAACmB,UAAU,CAACC,CAAC,IAAI;IAC3D,IAAIA,CAAC,CAACxC,yBAAyB,CAAC,CAAC,EAAE;MACjCqC,WAAW,WAAXA,WAAW,GAAXA,WAAW,GAAKG,CAAC;MACjB,OAAO,KAAK;IACd;IACA,OACEA,CAAC,CAAC9E,UAAU,CAAC,CAAC,IACd8E,CAAC,CAACC,SAAS,CAAC,CAAC,IACbD,CAAC,CAACE,eAAe,CAAC;MAAEC,MAAM,EAAE;IAAM,CAAC,CAAC,IACpCH,CAAC,CAACI,sBAAsB,CAAC;MAAED,MAAM,EAAE;IAAM,CAAC,CAAC;EAE/C,CAAC,CAAyB;EAC1B,MAAME,aAAa,GAAGP,SAAS,CAACQ,aAAa,CAAC;IAAEC,IAAI,EAAE;EAAc,CAAC,CAAC;EAEtE,IAAIT,SAAS,CAACI,eAAe,CAAC,CAAC,IAAIJ,SAAS,CAACM,sBAAsB,CAAC,CAAC,EAAE;IACrE,IAAIP,WAAW,EAAE;MACfC,SAAS,GAAGD,WAAW;IACzB,CAAC,MAAM,IAAI3B,gBAAgB,EAAE;MAK3BU,MAAM,CAACU,WAAW,CAChB3F,cAAc,CACZJ,uBAAuB,CAAC,EAAE,EAAEwB,YAAY,CAAC6D,MAAM,CAAC7C,IAAI,CAAC,CAAC,EACtD,EACF,CACF,CAAC;MACD+D,SAAS,GAAGlB,MAAM,CAACpC,GAAG,CAAC,QAAQ,CAAwC;MACvEoC,MAAM,GAAGkB,SAAS,CAACtD,GAAG,CAAC,MAAM,CAAmC;IAClE,CAAC,MAAM;MACL,MAAMoC,MAAM,CAACf,mBAAmB,CAC9B,iDACF,CAAC;IACH;EACF;EAEA,MAAM;IAAE2C,SAAS;IAAEC,cAAc;IAAEC,cAAc;IAAEC,UAAU;IAAEC;EAAW,CAAC,GACzEC,mBAAmB,CAACjC,MAAM,CAAC;EAG7B,IAAIyB,aAAa,IAAIO,UAAU,CAACE,MAAM,GAAG,CAAC,EAAE;IAC1C,IAAI,CAAC5C,gBAAgB,EAAE;MACrB,MAAM0C,UAAU,CAAC,CAAC,CAAC,CAAC/C,mBAAmB,CACrC,wDAAwD,GACtD,0FAA0F,GAC1F,2EACJ,CAAC;IACH;IACA,IAAI,CAACM,wBAAwB,EAAE;MAE7B,MAAMyC,UAAU,CAAC,CAAC,CAAC,CAAC/C,mBAAmB,CACrC,mDAAmD,GACjD,0HAA0H,GAC1H,2EACJ,CAAC;IACH;IACA,MAAM8B,aAA2C,GAAG,EAAE;IACtDG,SAAS,CAACiB,QAAQ,CAACxB,oBAAoB,EAAE;MAAEI;IAAc,CAAC,CAAC;IAC3D,MAAMqB,YAAY,GAAGC,eAAe,CAACnB,SAAS,CAAC;IAC/CH,aAAa,CAACuB,OAAO,CAACC,SAAS,IAAI;MACjC,MAAMC,MAAM,GAAGtH,UAAU,CAACkH,YAAY,CAAC;MACvCI,MAAM,CAACC,GAAG,GAAGF,SAAS,CAACpF,IAAI,CAACqF,MAAM,CAACC,GAAG;MAEtCF,SAAS,CAAC3E,GAAG,CAAC,QAAQ,CAAC,CAAC8C,WAAW,CAAC8B,MAAM,CAAC;IAC7C,CAAC,CAAC;EACJ;EAGA,IAAIX,cAAc,CAACK,MAAM,GAAG,CAAC,EAAE;IAC7B,MAAMQ,gBAAgB,GAAGC,UAAU,CAACzB,SAAS,EAAE,WAAW,EAAE,MAAM;MAChE,MAAM0B,IAAI,GAAGA,CAAA,KAAM1H,UAAU,CAAC,WAAW,CAAC;MAC1C,IAAIgG,SAAS,CAACf,KAAK,CAACf,IAAI,CAACiC,SAAS,CAAC,CAAC,EAAE;QACpC,OAAOrG,qBAAqB,CAC1BH,gBAAgB,CACd,KAAK,EACLuB,eAAe,CAAC,QAAQ,EAAEwG,IAAI,CAAC,CAAC,CAAC,EACjC7G,aAAa,CAAC,WAAW,CAC3B,CAAC,EACDmF,SAAS,CAACf,KAAK,CAAC0C,kBAAkB,CAAC,CAAC,EACpCD,IAAI,CAAC,CACP,CAAC;MACH,CAAC,MAAM;QACL,OAAOA,IAAI,CAAC,CAAC;MACf;IACF,CAAC,CAAC;IAEFf,cAAc,CAACS,OAAO,CAACQ,cAAc,IAAI;MACvC,MAAMC,OAAO,GAAG7H,UAAU,CAACwH,gBAAgB,CAAC;MAC5CK,OAAO,CAACN,GAAG,GAAGK,cAAc,CAAC3F,IAAI,CAACsF,GAAG;MAErCK,cAAc,CAACpC,WAAW,CAACqC,OAAO,CAAC;IACrC,CAAC,CAAC;EACJ;EAGA,IAAIjB,cAAc,CAACI,MAAM,GAAG,CAAC,EAAE;IAC7B,MAAMc,gBAAgB,GAAGL,UAAU,CAACzB,SAAS,EAAE,WAAW,EAAE,MAC1D1F,YAAY,CAACN,UAAU,CAAC,KAAK,CAAC,EAAEA,UAAU,CAAC,QAAQ,CAAC,CACtD,CAAC;IAED4G,cAAc,CAACQ,OAAO,CAACW,WAAW,IAAI;MACpC,MAAMC,SAAS,GAAGhI,UAAU,CAAC8H,gBAAgB,CAAC;MAC9CE,SAAS,CAACT,GAAG,GAAGQ,WAAW,CAAC9F,IAAI,CAACsF,GAAG;MAEpCQ,WAAW,CAACvC,WAAW,CAACwC,SAAS,CAAC;IACpC,CAAC,CAAC;EACJ;EAGA,IAAInB,UAAU,CAACG,MAAM,GAAG,CAAC,EAAE;IACzB,IAAI,CAAC5C,gBAAgB,EAAE;MACrB,MAAMyC,UAAU,CAAC,CAAC,CAAC,CAAC9C,mBAAmB,CACrC,wDAAwD,GACtD,6FAA6F,GAC7F,2EACJ,CAAC;IACH;IAEA,MAAMkE,cAA8C,GAAGpB,UAAU,CAACqB,MAAM,CACtE,CAACC,GAAG,EAAEC,SAAS,KAAKD,GAAG,CAACE,MAAM,CAACC,wBAAwB,CAACF,SAAS,CAAC,CAAC,EACnE,EACF,CAAC;IAEDH,cAAc,CAACb,OAAO,CAACgB,SAAS,IAAI;MAClC,MAAMrG,GAAG,GAAGqG,SAAS,CAACnG,IAAI,CAACK,QAAQ,GAC/B,EAAE,GAEF8F,SAAS,CAAC1F,GAAG,CAAC,UAAU,CAAC,CAACT,IAAI,CAACM,IAAI;MAEvC,MAAMgG,eAAe,GAAGH,SAAS,CAAC/E,UAAU;MAE5C,MAAMmF,YAAY,GAAGD,eAAe,CAACE,sBAAsB,CAAC;QAC1DC,IAAI,EAAEN,SAAS,CAACnG;MAClB,CAAC,CAAC;MACF,MAAM0G,MAAM,GAAGJ,eAAe,CAACK,gBAAgB,CAAC;QAC9CtB,MAAM,EAAEc,SAAS,CAACnG;MACpB,CAAC,CAAC;MACF,MAAM4G,gBAAgB,GAAGN,eAAe,CAACO,0BAA0B,CAAC;QAClEC,GAAG,EAAEX,SAAS,CAACnG;MACjB,CAAC,CAAC;MACF,MAAMiF,YAAY,GAAG8B,mBAAmB,CAAChD,SAAS,EAAEwC,YAAY,EAAEzG,GAAG,CAAC;MAEtE,MAAM2F,IAAoB,GAAG,EAAE;MAC/B,IAAIU,SAAS,CAACnG,IAAI,CAACK,QAAQ,EAAE;QAE3BoF,IAAI,CAACtE,IAAI,CAACgF,SAAS,CAAC1F,GAAG,CAAC,UAAU,CAAC,CAACT,IAAoB,CAAC;MAC3D;MAEA,IAAIuG,YAAY,EAAE;QAChB,MAAMS,KAAK,GAAGV,eAAe,CAACtG,IAAI,CAACiH,KAAK;QACxCxB,IAAI,CAACtE,IAAI,CAAC6F,KAAK,CAAC;MAClB;MAEA,MAAM1F,IAAI,GAAG1D,cAAc,CAACG,UAAU,CAACkH,YAAY,CAAC,EAAEQ,IAAI,CAAC;MAE3D,IAAIiB,MAAM,EAAE;QACVJ,eAAe,CAAClD,gBAAgB,CAAC,WAAW,EAAErE,cAAc,CAAC,CAAC,CAAC;QAC/DoH,SAAS,CAAC5C,WAAW,CAACnF,gBAAgB,CAACkD,IAAI,EAAEvD,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;QAEjE0G,SAAS,CAACtD,IAAI,CACZmF,eAAe,CAAC7F,GAAG,CAAC,aAAa,CACnC,CAAC;MACH,CAAC,MAAM,IAAI8F,YAAY,EAAE;QAEvBD,eAAe,CAAC/C,WAAW,CAACjC,IAAI,CAAC;MACnC,CAAC,MAAM,IAAIsF,gBAAgB,EAAE;QAC3BT,SAAS,CAAC5C,WAAW,CACnB3F,cAAc,CAACQ,gBAAgB,CAACkD,IAAI,EAAEvD,UAAU,CAAC,MAAM,CAAC,EAAE,KAAK,CAAC,EAAE,CAChEgB,cAAc,CAAC,CAAC,CACjB,CACH,CAAC;QAED0F,SAAS,CAACtD,IAAI,CACZgF,SAAS,CAAC1F,GAAG,CAAC,aAAa,CAC7B,CAAC;MACH,CAAC,MAAM;QACL0F,SAAS,CAAC5C,WAAW,CAACjC,IAAI,CAAC;MAC7B;IACF,CAAC,CAAC;EACJ;EAGA,IAAIsB,WAA0B;EAC9B,IAAI6B,SAAS,CAACM,MAAM,GAAG,CAAC,IAAI,CAAC1C,WAAW,EAAE;IACxCO,WAAW,GAAGsE,cAAc,CAACnD,SAAS,EAAEO,aAAa,CAAC;IAEtD,IACEjC,WAAW,IAGViC,aAAa,IAAI6C,aAAa,CAACpD,SAAS,CAAE,EAC3C;MACAU,SAAS,CAACU,OAAO,CAACiC,SAAS,IAAI;QAC7B,MAAMC,OAAO,GAAGD,SAAS,CAACE,KAAK,CAAC,CAAC,GAC7BrJ,aAAa,CAAC2E,WAAW,CAAC,GAC1B7E,UAAU,CAAC6E,WAAW,CAAC;QAE3ByE,OAAO,CAAC/B,GAAG,GAAG8B,SAAS,CAACpH,IAAI,CAACsF,GAAG;QAChC8B,SAAS,CAAC7D,WAAW,CAAC8D,OAAO,CAAC;MAChC,CAAC,CAAC;MAEF,IAAI,CAAChF,WAAW,EAAEO,WAAW,GAAG,IAAI;IACtC;EACF;EAEA,OAAO;IAAEA,WAAW;IAAEC;EAAO,CAAC;AAChC;AAKA,SAAS0E,WAAWA,CAACC,EAAU,EAAmB;EAChD,OAAOrJ,iBAAiB,CAACsJ,QAAQ,CAACD,EAAE,CAAC;AACvC;AAEA,SAASnB,wBAAwBA,CAC/BF,SAAuC,EAGwB;EAC/D,IACEA,SAAS,CAAC/E,UAAU,CAACoF,sBAAsB,CAAC,CAAC,IAC7CL,SAAS,CAAC/E,UAAU,CAACpB,IAAI,CAAC0H,QAAQ,KAAK,GAAG,EAC1C;IACA,MAAMC,cAAc,GAAGxB,SAAS,CAAC/E,UAAU;IAE3C,MAAMoG,EAAE,GAAGG,cAAc,CAAC3H,IAAI,CAAC0H,QAAQ,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAEvC;IAEZ,MAAMZ,KAAK,GAAGW,cAAc,CAAC3H,IAAI,CAACiH,KAAK;IAEvC,MAAMY,mBAAmB,GAAGN,WAAW,CAACC,EAAE,CAAC;IAE3C,IAAIrB,SAAS,CAACnG,IAAI,CAACK,QAAQ,EAAE;MAO3B,MAAMyH,GAAG,GAAG3B,SAAS,CAACnD,KAAK,CAAC+E,6BAA6B,CAAC,KAAK,CAAC;MAEhE,MAAMC,MAAM,GAAG7B,SAAS,CAACnG,IAAI,CAACgI,MAAM;MACpC,MAAM/H,QAAQ,GAAGkG,SAAS,CAACnG,IAAI,CAACC,QAAwB;MAExD0H,cAAc,CACXlH,GAAG,CAAC,MAAM,CAAC,CACX8C,WAAW,CACVnF,gBAAgB,CACd4J,MAAM,EACNvK,oBAAoB,CAAC,GAAG,EAAEqK,GAAG,EAAE7H,QAAQ,CAAC,EACxC,IACF,CACF,CAAC;MAEH0H,cAAc,CACXlH,GAAG,CAAC,OAAO,CAAC,CACZ8C,WAAW,CACV0E,eAAe,CACbJ,mBAAmB,GAAG,GAAG,GAAGL,EAAE,EAC9BpJ,gBAAgB,CAAC4J,MAAM,EAAEjK,UAAU,CAAC+J,GAAG,CAACxH,IAAI,CAAC,EAAE,IAAmB,CAAC,EACnE0G,KACF,CACF,CAAC;IACL,CAAC,MAAM;MAOL,MAAMgB,MAAM,GAAG7B,SAAS,CAACnG,IAAI,CAACgI,MAAM;MACpC,MAAM/H,QAAQ,GAAGkG,SAAS,CAACnG,IAAI,CAACC,QAAwB;MAExD0H,cAAc,CACXlH,GAAG,CAAC,MAAM,CAAC,CACX8C,WAAW,CAACnF,gBAAgB,CAAC4J,MAAM,EAAE/H,QAAQ,CAAC,CAAC;MAElD0H,cAAc,CACXlH,GAAG,CAAC,OAAO,CAAC,CACZ8C,WAAW,CACV0E,eAAe,CACbJ,mBAAmB,GAAG,GAAG,GAAGL,EAAE,EAC9BpJ,gBAAgB,CAAC4J,MAAM,EAAEjK,UAAU,CAACkC,QAAQ,CAACK,IAAI,CAAC,CAAC,EACnD0G,KACF,CACF,CAAC;IACL;IAEA,IAAIa,mBAAmB,EAAE;MACvBF,cAAc,CAACpE,WAAW,CACxBrF,iBAAiB,CACfsJ,EAAE,EACFG,cAAc,CAAC3H,IAAI,CAACyG,IAAI,EACxBkB,cAAc,CAAC3H,IAAI,CAACiH,KACtB,CACF,CAAC;IACH,CAAC,MAAM;MACLU,cAAc,CAAC3H,IAAI,CAAC0H,QAAQ,GAAG,GAAG;IACpC;IAEA,OAAO,CACLC,cAAc,CAAClH,GAAG,CAAC,MAAM,CAAC,EAC1BkH,cAAc,CAAClH,GAAG,CAAC,OAAO,CAAC,CAACA,GAAG,CAAC,MAAM,CAAC,CACxC;EACH,CAAC,MAAM,IAAI0F,SAAS,CAAC/E,UAAU,CAAC8G,kBAAkB,CAAC,CAAC,EAAE;IACpD,MAAMC,UAAU,GAAGhC,SAAS,CAAC/E,UAAU;IAEvC,MAAM0G,GAAG,GAAG3B,SAAS,CAACnD,KAAK,CAAC+E,6BAA6B,CAAC,KAAK,CAAC;IAChE,MAAMK,WAAW,GAAGjC,SAAS,CAACnG,IAAI,CAACK,QAAQ,GACvC8F,SAAS,CAACnD,KAAK,CAAC+E,6BAA6B,CAAC,MAAM,CAAC,GACrD,IAAI;IAER,MAAMM,KAAqB,GAAG,CAC5B5K,oBAAoB,CAClB,GAAG,EACHqK,GAAG,EACH1J,gBAAgB,CACd+H,SAAS,CAACnG,IAAI,CAACgI,MAAM,EACrBI,WAAW,GACP3K,oBAAoB,CAClB,GAAG,EACH2K,WAAW,EACXjC,SAAS,CAACnG,IAAI,CAACC,QACjB,CAAC,GACDkG,SAAS,CAACnG,IAAI,CAACC,QAAQ,EAC3BkG,SAAS,CAACnG,IAAI,CAACK,QACjB,CACF,CAAC,EACD5C,oBAAoB,CAClB,GAAG,EACHW,gBAAgB,CACd+H,SAAS,CAACnG,IAAI,CAACgI,MAAM,EACrBI,WAAW,GAAGrK,UAAU,CAACqK,WAAW,CAAC9H,IAAI,CAAC,GAAG6F,SAAS,CAACnG,IAAI,CAACC,QAAQ,EACpEkG,SAAS,CAACnG,IAAI,CAACK,QACjB,CAAC,EACD3C,gBAAgB,CAEdyI,SAAS,CAAC/E,UAAU,CAACpB,IAAI,CAAC0H,QAAQ,CAAC,CAAC,CAAC,EACrC3J,UAAU,CAAC+J,GAAG,CAACxH,IAAI,CAAC,EACpBhC,cAAc,CAAC,CAAC,CAClB,CACF,CAAC,CACF;IAED,IAAI,CAAC6H,SAAS,CAAC/E,UAAU,CAACpB,IAAI,CAACsI,MAAM,EAAE;MACrCD,KAAK,CAAClH,IAAI,CAACpD,UAAU,CAAC+J,GAAG,CAACxH,IAAI,CAAC,CAAC;IAClC;IAEA6H,UAAU,CAAC5E,WAAW,CAAC7E,kBAAkB,CAAC2J,KAAK,CAAC,CAAC;IAEjD,MAAM5B,IAAI,GAAG0B,UAAU,CAAC1H,GAAG,CACzB,qBACF,CAAiC;IACjC,MAAMwG,KAAK,GAAGkB,UAAU,CAAC1H,GAAG,CAC1B,oBACF,CAAiC;IACjC,OAAO,CAACgG,IAAI,EAAEQ,KAAK,CAAC;EACtB;EAEA,OAAO,CAACd,SAAS,CAAC;EAElB,SAAS8B,eAAeA,CACtBT,EAAkB,EAClBf,IAAwB,EACxBQ,KAAmB,EACnB;IACA,IAAIO,EAAE,KAAK,GAAG,EAAE;MACd,OAAO/J,oBAAoB,CAAC,GAAG,EAAEgJ,IAAI,EAAEQ,KAAK,CAAC;IAC/C,CAAC,MAAM;MACL,OAAOvJ,gBAAgB,CAAC8J,EAAE,EAAEf,IAAI,EAAEQ,KAAK,CAAC;IAC1C;EACF;AACF;AAEA,SAASE,aAAaA,CAACpD,SAA+B,EAAE;EACtD,OACEA,SAAS,CAACQ,aAAa,CAAC,CAAC,IACzB,CAAC,CAAER,SAAS,CAAC3C,UAAU,CAACA,UAAU,CAACpB,IAAI,CAAauI,UAAU;AAElE;AAEA,MAAMC,sBAAsB,GAAG,IAAA/E,4BAAkB,EAG9C;EACDC,cAAcA,CAACC,KAAK,EAAE;IAAE8E,MAAM;IAAE7F;EAAY,CAAC,EAAE;IAC7C,IAAI,CAACe,KAAK,CAAClD,GAAG,CAAC,QAAQ,CAAC,CAACoD,OAAO,CAAC,CAAC,EAAE;IACpC,IAAI4E,MAAM,CAACC,GAAG,CAAC/E,KAAK,CAAC3D,IAAI,CAAC,EAAE;IAC5ByI,MAAM,CAACE,GAAG,CAAChF,KAAK,CAAC3D,IAAI,CAAC;IAEtB2D,KAAK,CAACiF,mBAAmB,CAAC,CACxBjF,KAAK,CAAC3D,IAAI,EACVvC,oBAAoB,CAAC,GAAG,EAAEM,UAAU,CAAC6E,WAAW,CAAC,EAAE7E,UAAU,CAAC,MAAM,CAAC,CAAC,CACvE,CAAC;EACJ;AACF,CAAC,CAAC;AAGF,SAASmJ,cAAcA,CACrBnD,SAA+B,EAC/BO,aAAsB,EACtB;EACA,OAAOkB,UAAU,CAACzB,SAAS,EAAE,MAAM,EAAEnB,WAAW,IAAI;IAClD,IAAI,CAAC0B,aAAa,IAAI,CAAC6C,aAAa,CAACpD,SAAS,CAAC,EAAE,OAAOhF,cAAc,CAAC,CAAC;IAExEgF,SAAS,CAACiB,QAAQ,CAACwD,sBAAsB,EAAE;MACzCC,MAAM,EAAE,IAAII,OAAO,CAAC,CAAC;MACrBjG;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AAGA,SAASsC,eAAeA,CAACnB,SAA+B,EAAE;EACxD,OAAOyB,UAAU,CAACzB,SAAS,EAAE,WAAW,EAAE,MAAM;IAC9C,MAAM+E,WAAW,GAAG/E,SAAS,CAACf,KAAK,CAACC,qBAAqB,CAAC,MAAM,CAAC;IACjE,OAAOzF,uBAAuB,CAC5B,CAACgB,WAAW,CAACsK,WAAW,CAAC,CAAC,EAC1BlL,cAAc,CAACkB,MAAM,CAAC,CAAC,EAAE,CAACH,aAAa,CAACZ,UAAU,CAAC+K,WAAW,CAACxI,IAAI,CAAC,CAAC,CAAC,CACxE,CAAC;EACH,CAAC,CAAC;AACJ;AAGA,SAASyG,mBAAmBA,CAC1BhD,SAA+B,EAC/BwC,YAAqB,EACrBwC,QAAgB,EAChB;EACA,MAAMvB,EAAE,GAAGjB,YAAY,GAAG,KAAK,GAAG,KAAK;EAEvC,OAAOf,UAAU,CAACzB,SAAS,EAAE,aAAayD,EAAE,IAAIuB,QAAQ,IAAI,EAAE,EAAE,EAAE,MAAM;IACtE,MAAMC,QAAQ,GAAG,EAAE;IAEnB,IAAIC,MAAM;IACV,IAAIF,QAAQ,EAAE;MAEZE,MAAM,GAAG7K,gBAAgB,CAACU,MAAM,CAAC,CAAC,EAAEf,UAAU,CAACgL,QAAQ,CAAC,CAAC;IAC3D,CAAC,MAAM;MACL,MAAMG,MAAM,GAAGnF,SAAS,CAACf,KAAK,CAACC,qBAAqB,CAAC,MAAM,CAAC;MAE5D+F,QAAQ,CAACG,OAAO,CAACD,MAAM,CAAC;MACxBD,MAAM,GAAG7K,gBAAgB,CACvBU,MAAM,CAAC,CAAC,EACRf,UAAU,CAACmL,MAAM,CAAC5I,IAAI,CAAC,EACvB,IACF,CAAC;IACH;IAEA,IAAIiG,YAAY,EAAE;MAChB,MAAM6C,UAAU,GAAGrF,SAAS,CAACf,KAAK,CAACC,qBAAqB,CAAC,OAAO,CAAC;MACjE+F,QAAQ,CAAC7H,IAAI,CAACiI,UAAU,CAAC;MAEzBH,MAAM,GAAGxL,oBAAoB,CAAC,GAAG,EAAEwL,MAAM,EAAElL,UAAU,CAACqL,UAAU,CAAC9I,IAAI,CAAC,CAAC;IACzE;IAEA,OAAO9C,uBAAuB,CAACwL,QAAQ,EAAEC,MAAM,CAAC;EAClD,CAAC,CAAC;AACJ;AAEA,SAASzD,UAAUA,CACjBzB,SAAmB,EACnBjE,GAAW,EACXqD,IAAoC,EACpC;EACA,MAAMkG,QAAQ,GAAG,UAAU,GAAGvJ,GAAG;EACjC,IAAIwJ,IAAwB,GAAGvF,SAAS,CAACwF,OAAO,CAACF,QAAQ,CAAC;EAC1D,IAAI,CAACC,IAAI,EAAE;IACT,MAAMpG,EAAE,GAAGa,SAAS,CAACf,KAAK,CAACC,qBAAqB,CAACnD,GAAG,CAAC;IACrDwJ,IAAI,GAAGpG,EAAE,CAAC5C,IAAI;IACdyD,SAAS,CAACyF,OAAO,CAACH,QAAQ,EAAEC,IAAI,CAAC;IAEjCvF,SAAS,CAACf,KAAK,CAAC7B,IAAI,CAAC;MACnB+B,EAAE,EAAEA,EAAE;MACNC,IAAI,EAAEA,IAAI,CAACmG,IAAI;IACjB,CAAC,CAAC;EACJ;EAEA,OAAOA,IAAI;AACb;AAUA,MAAMG,0BAA0B,GAAG,IAAAhG,4BAAkB,EAAY;EAC/DiG,cAAcA,CAAC/F,KAAK,EAAE;IAAEc;EAAU,CAAC,EAAE;IACnCA,SAAS,CAACtD,IAAI,CAACwC,KAAK,CAAC;EACvB,CAAC;EACDgG,aAAaA,CAAChG,KAAK,EAAE;IAAEc;EAAU,CAAC,EAAE;IAClC,IAAId,KAAK,CAAC3D,IAAI,CAACM,IAAI,KAAK,MAAM,EAAE;IAChC,IACE,CAACqD,KAAK,CAACvC,UAAU,CAACwI,qBAAqB,CAAC;MAAE5B,MAAM,EAAErE,KAAK,CAAC3D;IAAK,CAAC,CAAC,IAC/D,CAAC2D,KAAK,CAACvC,UAAU,CAACyI,mBAAmB,CAAC;MAAEvJ,IAAI,EAAEqD,KAAK,CAAC3D;IAAK,CAAC,CAAC,EAC3D;MACA;IACF;IAEAyE,SAAS,CAACtD,IAAI,CAACwC,KAAK,CAAC;EACvB,CAAC;EACDD,cAAcA,CAACC,KAAK,EAAE;IAAEkB;EAAW,CAAC,EAAE;IACpC,IAAIlB,KAAK,CAAClD,GAAG,CAAC,QAAQ,CAAC,CAACoD,OAAO,CAAC,CAAC,EAAEgB,UAAU,CAAC1D,IAAI,CAACwC,KAAK,CAAC;EAC3D,CAAC;EACDmG,gBAAgBA,CAACnG,KAAK,EAAE;IAAEiB;EAAW,CAAC,EAAE;IACtC,IAAIjB,KAAK,CAAClD,GAAG,CAAC,QAAQ,CAAC,CAACoD,OAAO,CAAC,CAAC,EAAEe,UAAU,CAACzD,IAAI,CAACwC,KAAK,CAAC;EAC3D,CAAC;EACDoG,UAAUA,CAACpG,KAAK,EAAE;IAAEe;EAAe,CAAC,EAAE;IACpC,IAAI,CAACf,KAAK,CAACqG,sBAAsB,CAAC;MAAE1J,IAAI,EAAE;IAAY,CAAC,CAAC,EAAE;IAE1D,IAAI2J,IAAI,GAAGtG,KAAK,CAACX,KAAK;IACtB,GAAG;MACD,IAAIiH,IAAI,CAACC,aAAa,CAAC,WAAW,CAAC,EAAE;QACnCD,IAAI,CAACE,MAAM,CAAC,WAAW,CAAC;QACxB;MACF;MACA,IAAIF,IAAI,CAAChI,IAAI,CAAC9C,UAAU,CAAC,CAAC,IAAI,CAAC8K,IAAI,CAAChI,IAAI,CAACR,yBAAyB,CAAC,CAAC,EAAE;QACpE;MACF;IACF,CAAC,QAASwI,IAAI,GAAGA,IAAI,CAACG,MAAM;IAE5B1F,cAAc,CAACvD,IAAI,CAACwC,KAAK,CAAC;EAC5B,CAAC;EACD0G,YAAYA,CAAC1G,KAAK,EAAE;IAAEgB;EAAe,CAAC,EAAE;IACtC,IAAI,CAAChB,KAAK,CAAClD,GAAG,CAAC,MAAM,CAAC,CAACzC,YAAY,CAAC;MAAEsC,IAAI,EAAE;IAAM,CAAC,CAAC,EAAE;IACtD,IAAI,CAACqD,KAAK,CAAClD,GAAG,CAAC,UAAU,CAAC,CAACzC,YAAY,CAAC;MAAEsC,IAAI,EAAE;IAAS,CAAC,CAAC,EAAE;IAE7DqE,cAAc,CAACxD,IAAI,CAACwC,KAAK,CAAC;EAC5B;AACF,CAAC,CAAC;AAEF,SAASmB,mBAAmBA,CAACjC,MAAgB,EAAE;EAC7C,MAAM4B,SAAiC,GAAG,EAAE;EAC5C,MAAMC,cAA2C,GAAG,EAAE;EACtD,MAAMC,cAA2C,GAAG,EAAE;EACtD,MAAMC,UAAmC,GAAG,EAAE;EAC9C,MAAMC,UAAmC,GAAG,EAAE;EAE9ChC,MAAM,CAACmC,QAAQ,CAACyE,0BAA0B,EAAE;IAC1ChF,SAAS;IACTC,cAAc;IACdC,cAAc;IACdC,UAAU;IACVC;EACF,CAAC,CAAC;EAEF,OAAO;IACLJ,SAAS;IACTC,cAAc;IACdC,cAAc;IACdC,UAAU;IACVC;EACF,CAAC;AACH;AAEO,SAASyF,sBAAsBA,CAAA,EAEX;EACzB,IAAI,CAAC,IAAI,CAACC,mBAAmB,CAAC,CAAC,IAAI,IAAI,CAACC,sBAAsB,CAAC,CAAC,EAAE;IAChE,MAAM,IAAI3J,KAAK,CAAC,0DAA0D,CAAC;EAC7E;EACA,IAAI,IAAI,CAAC4J,wBAAwB,CAAC,CAAC,IAAI,IAAI,CAAChK,GAAG,CAAC,YAAY,CAAC,CAACsE,MAAM,GAAG,CAAC,EAAE;IACxE,MAAM,IAAIlE,KAAK,CAAC,qDAAqD,CAAC;EACxE;EAEA,MAAM6J,WAAW,GAAG,IAAI,CAACjK,GAAG,CAAC,aAAa,CAAC;EAE3C,IAAI,IAAI,CAACkK,0BAA0B,CAAC,CAAC,EAAE;IACrC,MAAMC,qBAAqB,GACzBF,WAAW,CAAC7I,qBAAqB,CAAC,CAAC,IAAI6I,WAAW,CAACG,kBAAkB,CAAC,CAAC;IACzE,MAAMC,UAAU,GACdJ,WAAW,CAAC9I,oBAAoB,CAAC,CAAC,IAAI8I,WAAW,CAACK,iBAAiB,CAAC,CAAC;IAEvE,MAAM/H,KAAK,GAAG0H,WAAW,CAACM,OAAO,CAAC,CAAC,GAC/BN,WAAW,CAAC1H,KAAK,CAACoH,MAAM,GACxBM,WAAW,CAAC1H,KAAK;IAGrB,IAAIE,EAAE,GAAGwH,WAAW,CAAC1K,IAAI,CAACkD,EAAE;IAC5B,IAAI+H,uBAAuB,GAAG,KAAK;IAEnC,IAAI,CAAC/H,EAAE,EAAE;MACP+H,uBAAuB,GAAG,IAAI;MAE9B/H,EAAE,GAAGF,KAAK,CAACC,qBAAqB,CAAC,SAAS,CAAC;MAE3C,IAAI2H,qBAAqB,IAAIE,UAAU,EAAE;QACvCJ,WAAW,CAAC1K,IAAI,CAACkD,EAAE,GAAG3D,SAAS,CAAC2D,EAAE,CAAC;MACrC;IACF,CAAC,MAAM,IAAI4H,UAAU,IAAI9H,KAAK,CAACkI,UAAU,CAAChI,EAAE,CAAC5C,IAAI,CAAC,EAAE;MAClD2K,uBAAuB,GAAG,IAAI;MAE9B/H,EAAE,GAAGF,KAAK,CAACC,qBAAqB,CAACC,EAAE,CAAC5C,IAAI,CAAC;IAC3C;IAEA,MAAM6K,kBAAkB,GAAGP,qBAAqB,GAC5CF,WAAW,CAAC1K,IAAI,GAChBR,mBAAmB,CAAC,KAAK,EAAE,CACzBC,kBAAkB,CAChBF,SAAS,CAAC2D,EAAE,CAAC,EAEbwH,WAAW,CAAC1K,IACd,CAAC,CACF,CAAC;IAEN,MAAMoL,wBAAwB,GAAG1L,sBAAsB,CAAC,IAAI,EAAE,CAC5DC,eAAe,CAACJ,SAAS,CAAC2D,EAAE,CAAC,EAAEnF,UAAU,CAAC,SAAS,CAAC,CAAC,CACtD,CAAC;IAEF,IAAI,CAACsN,WAAW,CAACD,wBAAwB,CAAC;IAC1C,IAAI,CAAC7H,WAAW,CAAC4H,kBAAkB,CAAC;IAEpC,IAAIF,uBAAuB,EAAE;MAC3BjI,KAAK,CAACsI,mBAAmB,CAAC,IAAI,CAAC;IACjC;IAEA,OAAO,IAAI;EACb,CAAC,MAAM,IAAI,IAAI,CAAC7K,GAAG,CAAC,YAAY,CAAC,CAACsE,MAAM,GAAG,CAAC,EAAE;IAC5C,MAAM,IAAIlE,KAAK,CAAC,qDAAqD,CAAC;EACxE;EAEA,MAAM0K,kBAAkB,GAAGb,WAAW,CAACc,0BAA0B,CAAC,CAAC;EAEnE,MAAMC,UAAU,GAAGC,MAAM,CAACC,IAAI,CAACJ,kBAAkB,CAAC,CAACK,GAAG,CAACtL,IAAI,IAAI;IAC7D,OAAOX,eAAe,CAAC5B,UAAU,CAACuC,IAAI,CAAC,EAAEvC,UAAU,CAACuC,IAAI,CAAC,CAAC;EAC5D,CAAC,CAAC;EAEF,MAAMuL,WAAW,GAAGnM,sBAAsB,CAAC,IAAI,EAAE+L,UAAU,CAAC;EAE5D,IAAI,CAACJ,WAAW,CAACQ,WAAW,CAAC;EAC7B,IAAI,CAACtI,WAAW,CAACmH,WAAW,CAAC1K,IAAI,CAAC;EAClC,OAAO,IAAI;AACb;AAEA,MAAM8L,yBAGJ,GAAG;EACH,wCAAwCC,CACtC9J,IAA4B,EAC5B+J,KAAK,EACL;IAEA,IAAI/J,IAAI,CAACjC,IAAI,CAACM,IAAI,KAAK0L,KAAK,CAAC1L,IAAI,EAAE;IACnC0L,KAAK,CAACC,WAAW,GAAG,IAAI;IACxBhK,IAAI,CAACiK,IAAI,CAAC,CAAC;EACb,CAAC;EACDC,KAAKA,CAAClK,IAAI,EAAE+J,KAAK,EAAE;IACjB,IAAI/J,IAAI,CAACe,KAAK,CAACkH,aAAa,CAAC8B,KAAK,CAAC1L,IAAI,CAAC,EAAE;MACxC2B,IAAI,CAACmK,IAAI,CAAC,CAAC;IACb;EACF;AACF,CAAC;AAEM,SAASzJ,kBAAkBA,CAEb0J,gBAAyB,EAAsB;EAClE,IAAI,IAAI,CAACrM,IAAI,CAACkD,EAAE,EAAE,OAAO,IAAI;EAE7B,MAAMoJ,GAAG,GAAGhN,eAAe,CAAC,IAAI,CAACU,IAAI,EAAE,IAAI,CAACoK,MAAM,CAAC;EACnD,IAAIkC,GAAG,IAAI,IAAI,EAAE,OAAO,IAAI;EAC5B,IAAI;IAAEhM;EAAK,CAAC,GAAGgM,GAAG;EAElB,IAAI,CAACD,gBAAgB,IAAI,iBAAiB,CAACE,IAAI,CAACjM,IAAI,CAAC,EAAE;IACrD,OAAO,IAAI;EACb;EAEA,IAAIA,IAAI,CAACkM,UAAU,CAAC,MAAM,CAAC,IAAIlM,IAAI,CAACkM,UAAU,CAAC,MAAM,CAAC,EAAE;IAEtD,OAAO,IAAI;EACb;EAEAlM,IAAI,GAAGpB,uBAAuB,CAACoB,IAAI,CAACmM,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;EAC1D,MAAMvJ,EAAE,GAAGnF,UAAU,CAACuC,IAAI,CAAC;EAC3BV,QAAQ,CAACsD,EAAE,EAAEoJ,GAAG,CAACI,YAAY,CAAC;EAE9B,MAAMV,KAAK,GAAG;IAAEC,WAAW,EAAE,KAAK;IAAE3L;EAAK,CAAC;EAK1C,MAAM;IAAE0C;EAAM,CAAC,GAAG,IAAI;EACtB,MAAM2J,OAAO,GAAG3J,KAAK,CAAC4J,aAAa,CAACtM,IAAI,CAAC;EACzC,IAAIqM,OAAO,EAAE;IACX,IAAIA,OAAO,CAACnI,IAAI,KAAK,OAAO,EAAE;MAa5BwH,KAAK,CAACC,WAAW,GAAG,IAAI;IAC1B,CAAC,MAAM,CASP;EACF,CAAC,MAAM,IAAIjJ,KAAK,CAACoH,MAAM,CAACc,UAAU,CAAC5K,IAAI,CAAC,IAAI0C,KAAK,CAAC6J,SAAS,CAACvM,IAAI,CAAC,EAAE;IACjE,IAAI,CAAC0E,QAAQ,CAAC8G,yBAAyB,EAAEE,KAAK,CAAC;EACjD;EAEA,IAAI,CAACA,KAAK,CAACC,WAAW,EAAE;IACtB,IAAI,CAACjM,IAAI,CAACkD,EAAE,GAAGA,EAAE;IAGV;MAELF,KAAK,CAAC8J,gBAAgB,CAAC,CAAC,CAACC,UAAU,CAAC7J,EAAE,CAAC5C,IAAI,CAAC,GAAG,IAAI;IACrD;IACA,OAAO,IAAI;EACb;EAEA,IAAI0C,KAAK,CAACkI,UAAU,CAAChI,EAAE,CAAC5C,IAAI,CAAC,IAAI,CAAC0C,KAAK,CAAC6J,SAAS,CAAC3J,EAAE,CAAC5C,IAAI,CAAC,EAAE;IAE1D0C,KAAK,CAACmH,MAAM,CAACjH,EAAE,CAAC5C,IAAI,CAAC;IACrB,IAAI,CAACN,IAAI,CAACkD,EAAE,GAAGA,EAAE;IAGV;MAELF,KAAK,CAAC8J,gBAAgB,CAAC,CAAC,CAACC,UAAU,CAAC7J,EAAE,CAAC5C,IAAI,CAAC,GAAG,IAAI;IACrD;IACA,OAAO,IAAI;EACb;EAGA,IAAI,CAACnB,UAAU,CAAC,IAAI,CAACa,IAAI,CAAC,EAAE,OAAO,IAAI;EAIvC,MAAMF,GAAG,GAAGkD,KAAK,CAACC,qBAAqB,CAACC,EAAE,CAAC5C,IAAI,CAAC;EAGhD,MAAM0M,MAAM,GAAG,EAAE;EACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGC,gBAAgB,CAAC,IAAI,CAACnN,IAAI,CAAC,EAAEiN,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;IAC/DD,MAAM,CAAC7L,IAAI,CAAC6B,KAAK,CAACC,qBAAqB,CAAC,GAAG,CAAC,CAAC;EAC/C;EACA,MAAM3B,IAAI,GAAG8L,iBAAQ,CAACC,UAAU,CAACC,GAAG;AACtC,iBAAiBxN,GAAG;AACpB,iBAAiBoD,EAAE,IAAI8J,MAAM;AAC7B,iBAAiBzN,SAAS,CAACO,GAAG,CAAC;AAC/B;AACA;AACA,QAAQP,SAAS,CAAC2D,EAAE,CAAC;AACrB,iBAAiB3D,SAAS,CAACO,GAAG,CAAC;AAC/B;AACA;AACA,eAAeP,SAAS,CAAC2D,EAAE,CAAC;AAC5B,SAASlE,YAAY,CAAC,IAAI,CAACgB,IAAI,CAAC;AAChC,GAAuB;EAErB,OAAO,IAAI,CAACuD,WAAW,CAACjC,IAAI,CAAC,CAAC,CAAC,CAAC,CAACb,GAAG,CAAC,aAAa,CAAC;AACrD;AAEA,SAAS0M,gBAAgBA,CAACnN,IAAgB,EAAU;EAClD,MAAMuN,KAAK,GAAGvN,IAAI,CAACgN,MAAM,CAACQ,SAAS,CACjCC,KAAK,IAAIrO,mBAAmB,CAACqO,KAAK,CAAC,IAAIpO,aAAa,CAACoO,KAAK,CAC5D,CAAC;EACD,OAAOF,KAAK,KAAK,CAAC,CAAC,GAAGvN,IAAI,CAACgN,MAAM,CAACjI,MAAM,GAAGwI,KAAK;AAClD", "ignoreList": []}