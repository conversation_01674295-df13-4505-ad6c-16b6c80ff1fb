#!/usr/bin/env python3
"""
测试一级标题与后续内容的60px间距
- 一级标题后固定60px间距
- 验证间距的视觉效果
- 对比不同标题级别的间距
"""

import requests
import json
import base64

# 测试内容
test_content = """# 第一个标题测试60px间距

这是第一个一级标题后的内容。标题和这段文字之间应该有60px的间距。这个间距应该比普通段落间距更大，形成清晰的视觉分隔。

## 二级标题的间距对比

这是二级标题后的内容，使用默认的间距设置，应该比一级标题的间距小。

### 三级标题的间距对比

这是三级标题后的内容，也使用默认间距，用于对比效果。

# 第二个标题测试间距一致性

这是第二个一级标题后的内容。所有一级标题后的间距都应该是一致的60px，确保文档的视觉统一性。

## 间距规格说明

### 间距设计原理
- **一级标题后间距**：60px（约2.1cm）
- **二级标题后间距**：默认设置
- **三级标题后间距**：默认设置
- **段落间距**：默认设置

### 视觉层次效果
- 一级标题的大间距突出了章节分隔
- 形成清晰的视觉层次
- 提升阅读体验
- 增强文档专业性

# 第三个标题验证间距效果

这是第三个一级标题后的内容。通过多个标题的测试，我们可以验证60px间距的一致性和视觉效果。

## 间距测试要点

请检查以下内容：

1. **间距一致性**：所有一级标题后的间距是否都是60px
2. **视觉效果**：间距是否提供了良好的视觉分隔
3. **阅读体验**：间距是否有助于内容的阅读和理解
4. **层次对比**：与二三级标题的间距对比是否合适

### 技术实现

```python
# 一级标题后添加60px间距
story.append(heading_element)
story.append(Spacer(1, 60))  # 60px固定间距
```

### 设计考虑

60px间距的选择基于以下考虑：
- 足够大以突出章节分隔
- 不会过大影响页面利用率
- 与标题背景图片尺寸协调
- 符合专业文档设计标准

# 第四个标题最终验证

这是第四个一级标题后的内容，用于最终验证间距设置的效果。

## 预期效果

如果间距设置正确，您应该看到：

1. 每个一级标题后都有明显的空白间距
2. 间距大小一致，约为60px
3. 与二三级标题形成明显的层次对比
4. 整体视觉效果专业、清晰

### 间距优势

60px间距带来的优势：
- **清晰分隔**：章节之间界限分明
- **视觉舒适**：适当的留白提升阅读体验
- **专业外观**：符合出版标准的间距设计
- **层次明确**：强化了标题的重要性

# 总结

通过设置60px的固定间距，一级标题在文档中的地位更加突出，视觉层次更加清晰。

## 结论

60px间距设置成功实现了：
- 一致的视觉效果
- 清晰的内容分隔
- 专业的文档外观
- 良好的阅读体验
"""

# 配置
config = {
    "page_format": "A4",
    "margin_top": 2.0,
    "margin_bottom": 2.0,
    "margin_left": 2.0,
    "margin_right": 2.0,
    "font_family": "Noto Sans CJK SC",
    "font_size": 12,
    "line_height": 1.5,
    "paragraph_spacing": 6,
    "indent_first_line": True,
    "dpi": 300,
    "color_mode": "CMYK",
    "bleed": 3,
    "enable_hyphenation": True,
    "widow_orphan_control": True
}

def test_heading_spacing():
    """测试一级标题的60px间距"""
    url = "http://localhost:8000/api/pdf/preview"
    
    payload = {
        "content": test_content,
        "layout_config": config
    }
    
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()
        
        result = response.json()
        if result.get("success"):
            print("✅ 一级标题60px间距测试PDF生成成功")
            
            # 保存PDF文件用于查看
            pdf_data = result.get("pdf_data")
            if pdf_data:
                with open("test_heading_spacing.pdf", "wb") as f:
                    f.write(base64.b64decode(pdf_data))
                print("📄 PDF文件已保存为 test_heading_spacing.pdf")
                print("🔍 请检查：")
                print("   📏 一级标题后间距：60px (约2.1cm)")
                print("   🔄 间距一致性：所有一级标题相同间距")
                print("   📐 视觉层次：与二三级标题形成对比")
                print("   👁️ 视觉效果：清晰的内容分隔")
                print("   📖 阅读体验：舒适的留白空间")
                print("   🎨 专业外观：符合设计标准")
            
            return True
        else:
            print("❌ PDF生成失败:", result.get("message", "未知错误"))
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 测试一级标题60px间距...")
    print("=" * 60)
    
    success = test_heading_spacing()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 测试完成！")
        print("📋 60px间距特点：")
        print("   • 固定间距：60px (约2.1cm)")
        print("   • 一致性：所有一级标题相同")
        print("   • 视觉分隔：清晰的章节界限")
        print("   • 层次对比：突出一级标题重要性")
        print("   • 阅读体验：舒适的留白设计")
        print("   • 专业外观：符合出版标准")
    else:
        print("⚠️ 测试失败，请检查错误信息")
