#!/usr/bin/env python3
"""
简单的页眉测试 - 验证中文显示和奇偶页布局
"""

import requests
import json
import base64

# 简单的测试内容
test_content = """# 第一页测试

这是第一页内容，应该是单数页。
页眉文字应该靠左对齐，页码应该在左下角。

## 第一页内容

这里是一些测试内容，用于验证页眉和页码功能。

---

# 第二页测试

这是第二页内容，应该是双数页。
页眉文字应该靠右对齐，页码应该在右下角。

## 第二页内容

这里是第二页的测试内容。

为了确保有足够的内容生成第二页，这里添加更多文字：

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.

Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.

---

# 第三页测试

这是第三页内容，应该又是单数页。
页眉文字应该靠左对齐，页码应该在左下角。

## 验证内容

请检查：
1. 页眉文字："非学而思课堂材料，学员自由领取。"
2. 第1页：页眉靠左，页码在左下角
3. 第2页：页眉靠右，页码在右下角  
4. 第3页：页眉靠左，页码在左下角
"""

# 配置
config = {
    "page_format": "A4",
    "margin_top": 2.0,
    "margin_bottom": 2.0,
    "margin_left": 2.0,
    "margin_right": 2.0,
    "font_family": "Noto Sans CJK SC",
    "font_size": 12,
    "line_height": 1.5,
    "paragraph_spacing": 6,
    "indent_first_line": True,
    "dpi": 300,
    "color_mode": "CMYK",
    "bleed": 3,
    "enable_hyphenation": True,
    "widow_orphan_control": True
}

def test_simple_header():
    """简单的页眉测试"""
    url = "http://localhost:8000/api/pdf/preview"
    
    payload = {
        "content": test_content,
        "layout_config": config
    }
    
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()
        
        result = response.json()
        if result.get("success"):
            print("✅ 简单页眉测试PDF生成成功")
            
            # 保存PDF文件用于查看
            pdf_data = result.get("pdf_data")
            if pdf_data:
                with open("test_simple_header.pdf", "wb") as f:
                    f.write(base64.b64decode(pdf_data))
                print("📄 PDF文件已保存为 test_simple_header.pdf")
                print("🔍 请检查：")
                print("   📋 页眉文字：'非学而思课堂材料，学员自由领取。'")
                print("   📄 第1页：页眉靠左，页码在左下角")
                print("   📄 第2页：页眉靠右，页码在右下角")
                print("   📄 第3页：页眉靠左，页码在左下角")
            
            return True
        else:
            print("❌ PDF生成失败:", result.get("message", "未知错误"))
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 简单页眉和页码测试...")
    print("=" * 50)
    
    success = test_simple_header()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 测试完成！请查看生成的PDF文件")
    else:
        print("⚠️ 测试失败，请检查错误信息")
