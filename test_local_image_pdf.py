#!/usr/bin/env python3
"""
测试本地图片功能的脚本
"""

import requests
import json

# 测试文档内容，只包含本地图片
test_content = """# 本地图片测试文档

这是一个用于测试本地图片功能的文档。

## 本地图片测试

下面是一个本地图片的引用：

![测试图片](backend/test_images/test_image.png)

*图 1: 测试图片说明*

## 文本内容

这里是一些普通的文本内容，用来测试图片和文本的混合排版效果。

**粗体文本** 和 *斜体文本* 以及 `行内代码`。

### 代码块示例

```python
def process_image(image_path):
    \"\"\"处理图片\"\"\"
    return image_path
```

## 结论

本地图片功能测试完成。
"""

def test_local_image_pdf():
    """测试本地图片PDF生成功能"""
    
    # API端点
    url = "http://localhost:8000/api/pdf/generate"
    
    # 请求数据
    data = {
        "content": test_content,
        "layout_config": {
            "page_format": "A4",
            "font_size": 12,
            "line_height": 1.5,
            "margin_top": 2.0,
            "margin_bottom": 2.0,
            "margin_left": 2.0,
            "margin_right": 2.0,
            "paragraph_spacing": 6,
            "indent_first_line": True
        },
        "filename": "local_image_test.pdf"
    }
    
    try:
        print("正在生成包含本地图片的PDF...")
        response = requests.post(url, json=data)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ PDF生成成功!")
            print(f"文件名: {result.get('filename')}")
            print(f"文件大小: {result.get('file_size')} bytes")
            print(f"下载链接: http://localhost:8000{result.get('download_url')}")
        else:
            print(f"❌ PDF生成失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")

if __name__ == "__main__":
    test_local_image_pdf()
