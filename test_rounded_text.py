#!/usr/bin/env python3
"""
测试双括号圆角矩形文本功能
"""

import asyncio
import sys
import os

# 添加backend目录到Python路径
sys.path.append('backend')

from app.services.pdf_service import PDFService
from app.models.schemas import LayoutConfig

async def test_rounded_text():
    """测试双括号圆角矩形文本功能"""
    
    # 创建PDF服务实例
    pdf_service = PDFService()
    
    # 创建布局配置
    config = LayoutConfig(
        page_format="A4",
        margin_top=2.0,
        margin_bottom=2.0,
        margin_left=2.0,
        margin_right=2.0,
        font_size=12,
        line_height=1.5,
        paragraph_spacing=12,
        indent_first_line=False,
        font_family="Arial"
    )
    
    # 测试内容 - 包含双括号圆角矩形文本
    test_content = """# 双括号圆角矩形文本测试

本文档测试双括号"（（））"中的文本采用圆角矩形边框样式的功能。

## 基本双括号文本

这是一个包含（（北京市第二实验小学））的段落文本。

这是另一个包含（（圆角矩形边框））的测试段落。

## 多个双括号文本

这个段落包含多个双括号文本：（（第一个文本））和（（第二个文本））在同一行中。

## 混合内容测试

普通文本开始，然后是（（圆角矩形文本）），接着继续普通文本内容。

这里有（（学校名称））和其他（（重要信息））需要特殊标记。

## 长文本测试

这是一个较长的段落，其中包含（（北京市第二实验小学附属幼儿园））这样的长文本内容，测试圆角矩形是否能够正确适应文本长度。

## 特殊字符测试

包含数字的文本：（（2024年春季学期））

包含英文的文本：（（Beijing Primary School））

包含混合内容：（（北京市第2实验小学Class A））

## 编号列表中的双括号文本

1. 这是第一个列表项，包含（（重要标记））
2. 第二个列表项有（（特殊内容））需要标记
3. 第三个列表项：（（最终测试））

## 结论

如果双括号中的文本都显示为橙色边框的圆角矩形，则功能实现成功！
"""
    
    try:
        # 生成PDF
        print("正在生成双括号圆角矩形文本测试PDF...")
        pdf_path = await pdf_service.generate_pdf(
            content=test_content,
            config=config,
            filename="rounded_text_test.pdf"
        )
        
        print(f"✅ PDF生成成功: {pdf_path}")
        
        # 检查文件是否存在
        if os.path.exists(pdf_path):
            file_size = os.path.getsize(pdf_path)
            print(f"📄 文件大小: {file_size} 字节")
            
            # 尝试获取页数
            try:
                page_count = await pdf_service.get_page_count(pdf_path)
                print(f"📖 页数: {page_count}")
            except:
                print("📖 无法获取页数")
                
        else:
            print("❌ PDF文件未找到")
            
        print("\n" + "="*60)
        print("🎯 双括号圆角矩形文本测试完成！")
        print("双括号中的文本应该显示为橙色边框的圆角矩形。")
        print("请打开PDF文件查看效果。")
        print("="*60)
            
    except Exception as e:
        print(f"❌ 生成PDF失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_rounded_text())
