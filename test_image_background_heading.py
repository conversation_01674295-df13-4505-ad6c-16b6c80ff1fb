#!/usr/bin/env python3
"""
测试一级标题的背景图片效果
- 一级标题使用橙色圆角矩形背景图片
- 白色文字在橙色背景上
- 居中对齐
"""

import requests
import json
import base64

# 测试内容
test_content = """# 这是带背景图片的一级标题

这是一级标题下的普通段落内容。一级标题现在使用了橙色圆角矩形背景图片，文字为白色，居中显示。

## 这是二级标题

二级标题保持原有的样式，不使用背景图片。

### 这是三级标题

三级标题也保持原有样式。

# 第二个背景图片标题测试

这里是第二个一级标题的测试内容。每个一级标题都应该：

1. 使用橙色圆角矩形背景图片
2. 白色文字清晰显示
3. 居中对齐
4. 适当的高度和宽度
5. 与页面内容协调

## 样式对比

让我们看看不同级别标题的对比效果：

### 标题层级说明

- **一级标题**：橙色背景图片，白色文字，居中
- **二级标题**：普通样式，左对齐
- **三级标题**：普通样式，左对齐

# 功能验证标题

这个标题用于验证一级标题的背景图片是否正确显示。

## 验证要点

请检查以下内容：

1. 一级标题是否显示橙色圆角矩形背景
2. 文字是否为白色且清晰可读
3. 标题是否居中显示
4. 背景图片是否完整显示
5. 与页面其他元素是否协调

### 技术实现

```python
class ImageBackgroundHeading(Flowable):
    def draw(self):
        # 绘制背景图片
        canvas.drawImage(
            background_image_path,
            img_x, img_y,
            width=img_width,
            height=img_height
        )
        
        # 绘制白色文字
        canvas.setFillColor(colors.white)
        canvas.drawString(text_x, text_y, self.text)
```

# 最终测试标题

如果您看到这个标题具有橙色圆角矩形背景图片，说明功能实现成功！

## 结论

一级标题的背景图片功能应该能够：
- 显示美观的橙色圆角矩形背景
- 提供清晰的白色文字
- 增强文档的视觉效果
- 提升专业外观

# 总结

背景图片功能让一级标题更加醒目和美观，为文档增添了专业的视觉效果。
"""

# 配置
config = {
    "page_format": "A4",
    "margin_top": 2.0,
    "margin_bottom": 2.0,
    "margin_left": 2.0,
    "margin_right": 2.0,
    "font_family": "Noto Sans CJK SC",
    "font_size": 12,
    "line_height": 1.5,
    "paragraph_spacing": 6,
    "indent_first_line": True,
    "dpi": 300,
    "color_mode": "CMYK",
    "bleed": 3,
    "enable_hyphenation": True,
    "widow_orphan_control": True
}

def test_image_background_heading():
    """测试一级标题的背景图片"""
    url = "http://localhost:8000/api/pdf/preview"
    
    payload = {
        "content": test_content,
        "layout_config": config
    }
    
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()
        
        result = response.json()
        if result.get("success"):
            print("✅ 一级标题背景图片测试PDF生成成功")
            
            # 保存PDF文件用于查看
            pdf_data = result.get("pdf_data")
            if pdf_data:
                with open("test_image_background_heading.pdf", "wb") as f:
                    f.write(base64.b64decode(pdf_data))
                print("📄 PDF文件已保存为 test_image_background_heading.pdf")
                print("🔍 请检查：")
                print("   🖼️ 一级标题：橙色圆角矩形背景图片")
                print("   🎨 文字颜色：白色，清晰可读")
                print("   📐 对齐方式：居中显示")
                print("   📏 尺寸：适当的高度和宽度")
                print("   🎯 视觉效果：与页面协调")
                print("   📄 二三级标题：保持原有样式")
            
            return True
        else:
            print("❌ PDF生成失败:", result.get("message", "未知错误"))
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 测试一级标题背景图片...")
    print("=" * 60)
    
    success = test_image_background_heading()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 测试完成！")
        print("📋 一级标题背景图片特点：")
        print("   • 橙色圆角矩形背景")
        print("   • 白色文字清晰显示")
        print("   • 居中对齐")
        print("   • 适当的尺寸比例")
        print("   • 专业的视觉效果")
    else:
        print("⚠️ 测试失败，请检查错误信息")
