#!/usr/bin/env python3
"""
测试一级标题的图片样式效果
- 一级标题应用图片样式
- 居中排布
- 背景色和边框效果
"""

import requests
import json
import base64

# 测试内容
test_content = """# 这是一级标题 - 应用图片样式

这是一级标题下的普通段落内容。一级标题应该具有图片样式的外观，包括背景色、边框和居中对齐。

## 这是二级标题

二级标题保持原有的样式，不应用图片效果。

### 这是三级标题

三级标题也保持原有样式。

# 第二个一级标题测试

这里是第二个一级标题的测试内容。每个一级标题都应该：

1. 居中对齐
2. 具有背景色
3. 有边框效果
4. 更大的字体
5. 适当的内边距

## 样式对比

让我们看看不同级别标题的对比效果：

### 标题层级说明

- **一级标题**：图片样式，居中，背景色，边框
- **二级标题**：普通样式，左对齐
- **三级标题**：普通样式，左对齐

# 功能验证标题

这个标题用于验证一级标题的图片样式是否正确应用。

## 验证要点

请检查以下内容：

1. 一级标题是否居中显示
2. 一级标题是否有浅灰色背景
3. 一级标题是否有边框
4. 一级标题字体是否比其他标题更大
5. 一级标题是否有适当的内边距

### 技术实现

```python
heading1_style = ParagraphStyle(
    'Heading1',
    fontSize=config.font_size * 2.2,  # 更大字体
    alignment=TA_CENTER,  # 居中对齐
    backColor=colors.Color(0.95, 0.95, 0.95),  # 背景色
    borderColor=colors.Color(0.8, 0.8, 0.8),  # 边框
    borderWidth=1,
    borderPadding=15,  # 内边距
)
```

# 最终测试标题

如果您看到这个标题具有图片样式的外观（背景色、边框、居中对齐），说明功能实现成功！

## 结论

一级标题的图片样式功能应该能够：
- 让标题更加突出和美观
- 提供更好的视觉层次
- 增强文档的专业外观
"""

# 配置
config = {
    "page_format": "A4",
    "margin_top": 2.0,
    "margin_bottom": 2.0,
    "margin_left": 2.0,
    "margin_right": 2.0,
    "font_family": "Noto Sans CJK SC",
    "font_size": 12,
    "line_height": 1.5,
    "paragraph_spacing": 6,
    "indent_first_line": True,
    "dpi": 300,
    "color_mode": "CMYK",
    "bleed": 3,
    "enable_hyphenation": True,
    "widow_orphan_control": True
}

def test_heading_styles():
    """测试一级标题的图片样式"""
    url = "http://localhost:8000/api/pdf/preview"
    
    payload = {
        "content": test_content,
        "layout_config": config
    }
    
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()
        
        result = response.json()
        if result.get("success"):
            print("✅ 一级标题图片样式测试PDF生成成功")
            
            # 保存PDF文件用于查看
            pdf_data = result.get("pdf_data")
            if pdf_data:
                with open("test_heading_styles.pdf", "wb") as f:
                    f.write(base64.b64decode(pdf_data))
                print("📄 PDF文件已保存为 test_heading_styles.pdf")
                print("🔍 请检查：")
                print("   📋 一级标题：图片样式，居中对齐")
                print("   🎨 背景色：浅灰色背景")
                print("   🖼️ 边框：灰色边框效果")
                print("   📏 字体：比其他标题更大")
                print("   📐 内边距：适当的内边距")
                print("   📄 二三级标题：保持原有样式")
            
            return True
        else:
            print("❌ PDF生成失败:", result.get("message", "未知错误"))
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 测试一级标题图片样式...")
    print("=" * 60)
    
    success = test_heading_styles()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 测试完成！")
        print("📋 一级标题图片样式特点：")
        print("   • 居中对齐显示")
        print("   • 浅灰色背景")
        print("   • 灰色边框效果")
        print("   • 更大的字体尺寸")
        print("   • 适当的内边距")
        print("   • 深灰色文字颜色")
    else:
        print("⚠️ 测试失败，请检查错误信息")
