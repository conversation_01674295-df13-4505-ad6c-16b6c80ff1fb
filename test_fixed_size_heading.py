#!/usr/bin/env python3
"""
测试固定尺寸的一级标题背景图片
- 背景图片使用固定尺寸 (400px x 50px)
- 图片居中显示
- 高度固定为60px
"""

import requests
import json
import base64

# 测试内容
test_content = """# 固定尺寸背景图片标题

这是一级标题下的普通段落内容。一级标题现在使用固定尺寸的橙色圆角矩形背景图片。

## 固定尺寸规格

### 背景图片尺寸
- **宽度**：400px (约14.1cm)
- **高度**：50px (约1.8cm)
- **位置**：居中显示
- **容器高度**：60px (约2.1cm)

### 设计优势
- 尺寸一致性
- 视觉稳定性
- 跨页面统一
- 专业外观

# 第二个固定尺寸标题

这里测试第二个一级标题，应该与第一个标题具有完全相同的尺寸。

## 尺寸对比测试

让我们看看不同级别标题的对比效果：

### 标题层级说明

- **一级标题**：固定尺寸橙色背景图片，白色文字，居中
- **二级标题**：普通样式，左对齐
- **三级标题**：普通样式，左对齐

# 功能验证标题

这个标题用于验证固定尺寸背景图片的一致性。

## 验证要点

请检查以下内容：

1. 所有一级标题背景图片尺寸是否一致
2. 图片是否在容器中居中显示
3. 文字是否在图片中央
4. 整体视觉效果是否协调
5. 与页面其他元素的比例是否合适

### 技术实现

```python
# 固定背景图片尺寸
fixed_img_width = 400  # 14.1cm
fixed_img_height = 50  # 1.8cm

# 计算居中位置
img_x = (self.width - fixed_img_width) / 2
img_y = (self.height - fixed_img_height) / 2

# 绘制固定尺寸背景图片
canvas.drawImage(
    background_image_path,
    img_x, img_y,
    width=fixed_img_width,
    height=fixed_img_height
)
```

# 最终测试标题

如果所有一级标题的背景图片尺寸都完全一致，说明固定尺寸功能实现成功！

## 结论

固定尺寸的背景图片功能提供了：
- 一致的视觉效果
- 稳定的布局表现
- 专业的文档外观
- 可预测的设计结果

# 总结

固定尺寸功能确保了文档中所有一级标题的视觉一致性，提升了整体的专业性。
"""

# 配置
config = {
    "page_format": "A4",
    "margin_top": 2.0,
    "margin_bottom": 2.0,
    "margin_left": 2.0,
    "margin_right": 2.0,
    "font_family": "Noto Sans CJK SC",
    "font_size": 12,
    "line_height": 1.5,
    "paragraph_spacing": 6,
    "indent_first_line": True,
    "dpi": 300,
    "color_mode": "CMYK",
    "bleed": 3,
    "enable_hyphenation": True,
    "widow_orphan_control": True
}

def test_fixed_size_heading():
    """测试固定尺寸的一级标题背景图片"""
    url = "http://localhost:8000/api/pdf/preview"
    
    payload = {
        "content": test_content,
        "layout_config": config
    }
    
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()
        
        result = response.json()
        if result.get("success"):
            print("✅ 固定尺寸一级标题测试PDF生成成功")
            
            # 保存PDF文件用于查看
            pdf_data = result.get("pdf_data")
            if pdf_data:
                with open("test_fixed_size_heading.pdf", "wb") as f:
                    f.write(base64.b64decode(pdf_data))
                print("📄 PDF文件已保存为 test_fixed_size_heading.pdf")
                print("🔍 请检查：")
                print("   📏 背景图片尺寸：400px x 50px (固定)")
                print("   📐 容器高度：60px (固定)")
                print("   🎯 位置：居中显示")
                print("   🔄 一致性：所有一级标题尺寸相同")
                print("   🎨 文字：白色，居中对齐")
                print("   📄 二三级标题：保持原有样式")
            
            return True
        else:
            print("❌ PDF生成失败:", result.get("message", "未知错误"))
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 测试固定尺寸一级标题背景图片...")
    print("=" * 60)
    
    success = test_fixed_size_heading()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 测试完成！")
        print("📋 固定尺寸背景图片特点：")
        print("   • 固定宽度：400px (约14.1cm)")
        print("   • 固定高度：50px (约1.8cm)")
        print("   • 容器高度：60px (约2.1cm)")
        print("   • 居中显示：自动计算居中位置")
        print("   • 尺寸一致：所有一级标题相同尺寸")
        print("   • 专业外观：稳定的视觉效果")
    else:
        print("⚠️ 测试失败，请检查错误信息")
