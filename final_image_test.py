#!/usr/bin/env python3
"""
最终的图片功能测试
"""

import requests
import json
import os

def test_pdf_list():
    """测试PDF列表功能"""
    url = "http://localhost:8000/api/pdf/list"
    
    try:
        response = requests.get(url)
        if response.status_code == 200:
            result = response.json()
            print(f"📋 PDF列表获取成功，共 {result.get('total', 0)} 个文件:")
            for pdf in result.get('pdfs', [])[:5]:  # 只显示前5个
                print(f"  - {pdf.get('filename')} ({pdf.get('size')} bytes)")
        else:
            print(f"❌ 获取PDF列表失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def test_comprehensive_image_pdf():
    """测试综合图片PDF生成功能"""
    
    # 测试文档内容
    test_content = """# PrintMind 图片功能测试报告

## 测试概述

本文档用于测试 PrintMind 的图片处理功能，包括本地图片的导入和显示。

## 本地图片测试

### 测试图片 1

![测试图片](test_image.png)

*图 1: 基本图片测试*

### 测试图片 2（完整路径）

![测试图片完整路径](test_images/test_image.png)

*图 2: 使用完整路径的图片测试*

## 功能验证

### ✅ 已实现的功能

1. **图片识别**: 能够识别 Markdown 中的图片语法 `![alt](src)`
2. **路径解析**: 支持多种图片路径格式
3. **图片缓存**: 为网络图片提供缓存机制
4. **图片缩放**: 自动调整图片尺寸适应PDF布局
5. **错误处理**: 图片加载失败时的优雅降级

### 🔄 处理流程

```
Markdown 内容 → 图片识别 → 路径解析 → 图片加载 → 尺寸调整 → PDF嵌入
```

### 📊 技术规格

- **支持格式**: PNG, JPEG, GIF
- **最大尺寸**: 400x300 像素（自动缩放）
- **缓存机制**: 网络图片本地缓存
- **错误处理**: 显示 alt 文本作为替代

## 测试结果

通过本次测试，验证了 PrintMind 的图片处理功能能够：

1. 正确识别和解析图片语法
2. 处理不同的图片路径格式
3. 在PDF中正确显示图片
4. 提供图片说明文字支持
5. 优雅处理图片加载错误

## 结论

PrintMind 的图片功能已经基本实现，能够满足文档中插入图片的需求。

---

*测试完成时间: 2024年12月6日*
"""

    # API端点
    url = "http://localhost:8000/api/pdf/generate"
    
    # 请求数据
    data = {
        "content": test_content,
        "layout_config": {
            "page_format": "A4",
            "font_size": 12,
            "line_height": 1.5,
            "margin_top": 2.0,
            "margin_bottom": 2.0,
            "margin_left": 2.0,
            "margin_right": 2.0,
            "paragraph_spacing": 6,
            "indent_first_line": True
        },
        "filename": "comprehensive_image_test.pdf"
    }
    
    try:
        print("🔄 正在生成综合图片测试PDF...")
        response = requests.post(url, json=data)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ PDF生成成功!")
            print(f"📄 文件名: {result.get('filename', 'comprehensive_image_test.pdf')}")
            print(f"📊 文件大小: {result.get('file_size')} bytes")
            
            # 检查文件是否真的存在
            pdf_path = "backend/generated_pdfs/comprehensive_image_test.pdf"
            if os.path.exists(pdf_path):
                actual_size = os.path.getsize(pdf_path)
                print(f"✅ 文件确认存在，实际大小: {actual_size} bytes")
            else:
                print(f"⚠️  文件路径可能不正确: {pdf_path}")
                
        else:
            print(f"❌ PDF生成失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def main():
    """主测试函数"""
    print("🚀 开始 PrintMind 图片功能综合测试\n")
    
    print("1️⃣ 测试PDF列表功能:")
    test_pdf_list()
    print()
    
    print("2️⃣ 测试综合图片PDF生成:")
    test_comprehensive_image_pdf()
    print()
    
    print("3️⃣ 再次检查PDF列表:")
    test_pdf_list()
    print()
    
    print("🎉 测试完成!")

if __name__ == "__main__":
    main()
