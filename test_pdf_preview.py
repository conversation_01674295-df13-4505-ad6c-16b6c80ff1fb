#!/usr/bin/env python3
"""
测试PDF预览功能
"""

import requests
import json
import base64

def test_pdf_preview():
    """测试PDF预览功能"""
    
    # 测试文档内容，包含图片
    test_content = """# PDF预览测试文档

这是一个用于测试PDF预览功能的文档。

## 图片测试

下面是一个本地图片：

![测试图片](test_image.png)

*图 1: 测试图片说明*

## 文本内容

这里是一些普通的文本内容。

**粗体文本** 和 *斜体文本* 以及 `行内代码`。

### 代码块示例

```python
def test_preview():
    print("PDF预览测试")
    return True
```

## 结论

PDF预览功能测试完成。
"""

    # API端点
    url = "http://localhost:8000/api/pdf/preview"
    
    # 请求数据
    data = {
        "content": test_content,
        "layout_config": {
            "page_format": "A4",
            "font_size": 12,
            "line_height": 1.5,
            "margin_top": 2.0,
            "margin_bottom": 2.0,
            "margin_left": 2.0,
            "margin_right": 2.0,
            "paragraph_spacing": 6,
            "indent_first_line": True
        }
    }
    
    try:
        print("🔄 正在生成PDF预览...")
        response = requests.post(url, json=data)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ PDF预览生成成功!")
            
            # 检查返回的数据
            pdf_data = result.get('pdf_data')
            if pdf_data:
                # 解码base64数据检查大小
                pdf_bytes = base64.b64decode(pdf_data)
                print(f"📊 PDF数据大小: {len(pdf_bytes)} bytes")
                
                # 检查是否是有效的PDF数据
                if pdf_bytes.startswith(b'%PDF'):
                    print("✅ PDF数据格式正确")
                    
                    # 可选：保存预览PDF到文件进行检查
                    with open('preview_test.pdf', 'wb') as f:
                        f.write(pdf_bytes)
                    print("💾 预览PDF已保存为 preview_test.pdf")
                else:
                    print("❌ PDF数据格式不正确")
            else:
                print("❌ 没有返回PDF数据")
                
        else:
            print(f"❌ PDF预览生成失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def test_pdf_generation_comparison():
    """对比PDF生成和预览功能"""
    
    test_content = """# 对比测试

![测试图片](test_image.png)

这是对比测试内容。"""
    
    config = {
        "page_format": "A4",
        "font_size": 12,
        "line_height": 1.5,
        "margin_top": 2.0,
        "margin_bottom": 2.0,
        "margin_left": 2.0,
        "margin_right": 2.0,
        "paragraph_spacing": 6,
        "indent_first_line": True
    }
    
    print("\n=== 对比测试 ===")
    
    # 测试PDF生成
    try:
        response = requests.post("http://localhost:8000/api/pdf/generate", json={
            "content": test_content,
            "layout_config": config,
            "filename": "comparison_test.pdf"
        })
        if response.status_code == 200:
            result = response.json()
            print(f"📄 PDF生成: {result.get('file_size')} bytes")
        else:
            print(f"❌ PDF生成失败: {response.status_code}")
    except Exception as e:
        print(f"❌ PDF生成请求失败: {e}")
    
    # 测试PDF预览
    try:
        response = requests.post("http://localhost:8000/api/pdf/preview", json={
            "content": test_content,
            "layout_config": config
        })
        if response.status_code == 200:
            result = response.json()
            pdf_data = result.get('pdf_data')
            if pdf_data:
                pdf_bytes = base64.b64decode(pdf_data)
                print(f"👁️  PDF预览: {len(pdf_bytes)} bytes")
            else:
                print("❌ PDF预览没有返回数据")
        else:
            print(f"❌ PDF预览失败: {response.status_code}")
    except Exception as e:
        print(f"❌ PDF预览请求失败: {e}")

if __name__ == "__main__":
    print("🚀 开始PDF预览功能测试\n")
    
    print("1️⃣ 测试PDF预览功能:")
    test_pdf_preview()
    
    print("\n2️⃣ 对比PDF生成和预览:")
    test_pdf_generation_comparison()
    
    print("\n🎉 测试完成!")
