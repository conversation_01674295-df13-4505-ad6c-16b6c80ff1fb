# PrintMind 统一编辑预览界面测试

这是一个用于测试新的统一编辑预览界面的文档。

## 功能特性

### 🎯 三种视图模式

1. **编辑模式**：专注于内容编辑
2. **分屏模式**：同时显示编辑器和预览
3. **预览模式**：专注于预览效果

### 🖼️ 图片支持

![测试图片](test_image.png)

*图 1: 测试图片展示*

### 📝 文本格式

这里有一些**粗体文本**和*斜体文本*，还有`行内代码`。

### 📊 代码块

```python
def test_editor_preview():
    """测试编辑预览功能"""
    print("Hello, PrintMind!")
    return True
```

### 📋 列表

- 实时预览
- 图片支持
- 多种视图模式
- 专业排版

### 📈 表格

| 功能 | 状态 | 说明 |
|------|------|------|
| 编辑器 | ✅ | 支持Markdown语法 |
| HTML预览 | ✅ | 实时渲染 |
| PDF预览 | ✅ | 专业排版 |
| 图片处理 | ✅ | 自动优化 |

## 结论

新的统一编辑预览界面提供了更好的用户体验！
