#!/usr/bin/env python3
"""
测试页眉和页码功能
- 页眉文字："非学而思课堂材料，学员自由领取。"
- 单数页：页眉文字靠左，页码在左下角
- 双数页：页眉文字靠右，页码在右下角
"""

import requests
import json
import base64

# 测试内容 - 创建多页文档来测试奇偶页效果
test_content = """# 页眉和页码测试文档

这是一个用于测试页眉和页码功能的多页文档。

## 功能说明

本文档将测试以下功能：

1. **页眉文字**："非学而思课堂材料，学员自由领取。"
2. **单数页布局**：页眉文字靠左，页码在左下角
3. **双数页布局**：页眉文字靠右，页码在右下角

## 第一章 测试内容

这是第一章的内容。这一页应该是单数页，页眉文字应该靠左对齐，页码应该在左下角。

### 1.1 基础功能

为了生成足够的内容来创建多页文档，这里添加一些详细的说明文字。

页眉功能的实现需要考虑以下几个方面：
- 文字的位置和对齐方式
- 字体的选择和大小
- 颜色的搭配
- 与色条的协调

### 1.2 技术实现

在ReportLab中实现页眉和页码需要：
1. 重写PageTemplate的beforeDrawPage方法
2. 获取当前页码
3. 根据奇偶页调整布局
4. 使用合适的字体显示中文

## 第二章 详细测试

这是第二章的内容。这一页应该是双数页，页眉文字应该靠右对齐，页码应该在右下角。

### 2.1 布局验证

请检查以下内容：
- 顶部色条中的页眉文字位置
- 底部色条中的页码位置
- 文字是否清晰可读
- 颜色对比是否合适

### 2.2 多页测试

为了确保功能在多页文档中正常工作，我们需要创建足够的内容。

这里是一些填充内容：

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.

Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.

## 第三章 更多内容

这是第三章，应该又是单数页了。

### 3.1 代码示例

```python
def test_header_footer():
    # 测试页眉和页码功能
    header_text = "非学而思课堂材料，学员自由领取。"
    
    for page_num in range(1, 6):
        is_odd = page_num % 2 == 1
        if is_odd:
            print(f"第{page_num}页：页眉靠左，页码在左下角")
        else:
            print(f"第{page_num}页：页眉靠右，页码在右下角")
```

### 3.2 表格测试

| 页码 | 类型 | 页眉位置 | 页码位置 |
|------|------|----------|----------|
| 1 | 单数页 | 左对齐 | 左下角 |
| 2 | 双数页 | 右对齐 | 右下角 |
| 3 | 单数页 | 左对齐 | 左下角 |
| 4 | 双数页 | 右对齐 | 右下角 |

## 第四章 最终验证

这是第四章，应该是双数页。

### 4.1 检查清单

请验证以下内容：

- [ ] 页眉文字"非学而思课堂材料，学员自由领取。"正确显示
- [ ] 单数页页眉文字靠左对齐
- [ ] 双数页页眉文字靠右对齐
- [ ] 单数页页码在左下角
- [ ] 双数页页码在右下角
- [ ] 文字颜色为黑色，清晰可读
- [ ] 与色条搭配协调

### 4.2 结论

如果以上所有项目都正确显示，说明页眉和页码功能实现成功！

## 第五章 额外测试

这是第五章，用于确保有足够的页数来测试奇偶页功能。

这里添加更多内容来确保文档有多页：

1. 第一项内容
2. 第二项内容
3. 第三项内容
4. 第四项内容
5. 第五项内容

每一页都应该正确显示页眉和页码，位置根据奇偶页而不同。
"""

# 配置
config = {
    "page_format": "A4",
    "margin_top": 2.0,
    "margin_bottom": 2.0,
    "margin_left": 2.0,
    "margin_right": 2.0,
    "font_family": "Noto Sans CJK SC",
    "font_size": 12,
    "line_height": 1.5,
    "paragraph_spacing": 6,
    "indent_first_line": True,
    "dpi": 300,
    "color_mode": "CMYK",
    "bleed": 3,
    "enable_hyphenation": True,
    "widow_orphan_control": True
}

def test_header_footer():
    """测试页眉和页码功能"""
    url = "http://localhost:8000/api/pdf/preview"
    
    payload = {
        "content": test_content,
        "layout_config": config
    }
    
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()
        
        result = response.json()
        if result.get("success"):
            print("✅ 页眉和页码功能测试PDF生成成功")
            
            # 保存PDF文件用于查看
            pdf_data = result.get("pdf_data")
            if pdf_data:
                with open("test_header_footer.pdf", "wb") as f:
                    f.write(base64.b64decode(pdf_data))
                print("📄 PDF文件已保存为 test_header_footer.pdf")
                print("🔍 请打开PDF文件检查：")
                print("   📋 页眉文字：'非学而思课堂材料，学员自由领取。'")
                print("   📄 单数页：页眉靠左，页码在左下角")
                print("   📄 双数页：页眉靠右，页码在右下角")
                print("   🎨 文字颜色：黑色")
                print("   📐 位置：在色条内合适位置")
            
            return True
        else:
            print("❌ PDF生成失败:", result.get("message", "未知错误"))
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 测试页眉和页码功能...")
    print("=" * 60)
    
    success = test_header_footer()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 测试完成！")
        print("📋 功能说明：")
        print("   • 页眉文字：'非学而思课堂材料，学员自由领取。'")
        print("   • 单数页：页眉靠左，页码在左下角")
        print("   • 双数页：页眉靠右，页码在右下角")
        print("   • 符合印刷装订需要")
    else:
        print("⚠️ 测试失败，请检查错误信息")
