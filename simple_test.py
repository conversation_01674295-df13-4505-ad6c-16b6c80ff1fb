#!/usr/bin/env python3
"""
简单的API测试
"""

import requests

def test_pdf_list():
    """测试PDF列表"""
    try:
        response = requests.get("http://localhost:8000/api/pdf/list")
        print(f"PDF列表状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"PDF数量: {data.get('total', 0)}")
            for pdf in data.get('pdfs', [])[:3]:
                print(f"  - {pdf.get('filename')} ({pdf.get('size')} bytes)")
    except Exception as e:
        print(f"错误: {e}")

def test_simple_pdf():
    """测试简单PDF生成"""
    data = {
        "content": "# 简单测试\n\n![测试图片](test_image.png)\n\n这是一个简单的测试。",
        "layout_config": {
            "page_format": "A4",
            "font_size": 12,
            "line_height": 1.5,
            "margin_top": 2.0,
            "margin_bottom": 2.0,
            "margin_left": 2.0,
            "margin_right": 2.0,
            "paragraph_spacing": 6,
            "indent_first_line": True
        },
        "filename": "simple_test.pdf"
    }
    
    try:
        response = requests.post("http://localhost:8000/api/pdf/generate", json=data)
        print(f"PDF生成状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"响应内容: {result}")
        else:
            print(f"错误: {response.text}")
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    print("=== PDF列表测试 ===")
    test_pdf_list()
    print("\n=== PDF生成测试 ===")
    test_simple_pdf()
