#!/usr/bin/env python3
"""
测试圆形页码功能
- 页码显示在8mm直径的#f7ab00颜色圆形上
- 位置在底部黄条上方
- 奇偶页分别在左右两侧
"""

import requests
import json
import base64

# 测试内容
test_content = """# 圆形页码设计测试

这是一个用于测试新的圆形页码设计的文档。

## 设计规格

### 🎯 页码圆形
- **直径**：8mm
- **颜色**：#f7ab00（橙色）
- **位置**：底部黄条上方
- **文字**：白色页码数字

### 📍 位置规则
- **单数页**：圆形在左侧
- **双数页**：圆形在右侧
- **间距**：距离底部色条2mm

## 第一页测试

这是第一页内容，应该是单数页。
页码圆形应该在左侧，底部黄条上方。

### 验证要点
1. 圆形直径为8mm
2. 圆形颜色为#f7ab00
3. 页码数字为白色
4. 位置在底部黄条上方
5. 左侧对齐（单数页）

---

# 第二页测试

这是第二页内容，应该是双数页。
页码圆形应该在右侧，底部黄条上方。

## 布局验证

为了确保有足够的内容生成第二页，这里添加更多文字：

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.

Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.

### 第二页验证要点
1. 圆形直径为8mm
2. 圆形颜色为#f7ab00
3. 页码数字为白色
4. 位置在底部黄条上方
5. 右侧对齐（双数页）

---

# 第三页测试

这是第三页内容，应该又是单数页。
页码圆形应该在左侧，底部黄条上方。

## 多页一致性测试

这里测试多页文档中圆形页码的一致性：

### 页码圆形特点
- 醒目的橙色设计
- 清晰的白色数字
- 精确的位置控制
- 与底部色条协调

### 技术实现
```python
# 页码圆形参数
circle_diameter = 8  # 8mm直径
circle_color = "#f7ab00"  # 橙色
text_color = "white"  # 白色文字

# 位置计算
circle_y = bottom_band_height + circle_radius + 2  # 底部色条上方2mm
```

## 结论

如果您看到：
- 每页底部都有橙色圆形
- 圆形中显示白色页码数字
- 单数页圆形在左侧
- 双数页圆形在右侧
- 圆形位于底部黄条上方

说明新的圆形页码设计实现成功！
"""

# 配置
config = {
    "page_format": "A4",
    "margin_top": 2.0,
    "margin_bottom": 2.0,
    "margin_left": 2.0,
    "margin_right": 2.0,
    "font_family": "Noto Sans CJK SC",
    "font_size": 12,
    "line_height": 1.5,
    "paragraph_spacing": 6,
    "indent_first_line": True,
    "dpi": 300,
    "color_mode": "CMYK",
    "bleed": 3,
    "enable_hyphenation": True,
    "widow_orphan_control": True
}

def test_circle_page_numbers():
    """测试圆形页码功能"""
    url = "http://localhost:8000/api/pdf/preview"
    
    payload = {
        "content": test_content,
        "layout_config": config
    }
    
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()
        
        result = response.json()
        if result.get("success"):
            print("✅ 圆形页码测试PDF生成成功")
            
            # 保存PDF文件用于查看
            pdf_data = result.get("pdf_data")
            if pdf_data:
                with open("test_circle_page_numbers.pdf", "wb") as f:
                    f.write(base64.b64decode(pdf_data))
                print("📄 PDF文件已保存为 test_circle_page_numbers.pdf")
                print("🔍 请检查：")
                print("   🎯 页码圆形：8mm直径，#f7ab00颜色")
                print("   📍 位置：底部黄条上方2mm")
                print("   📄 第1页：圆形在左侧")
                print("   📄 第2页：圆形在右侧")
                print("   📄 第3页：圆形在左侧")
                print("   🎨 页码文字：白色，居中显示")
            
            return True
        else:
            print("❌ PDF生成失败:", result.get("message", "未知错误"))
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 测试圆形页码设计...")
    print("=" * 60)
    
    success = test_circle_page_numbers()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 测试完成！")
        print("📋 新的页码设计：")
        print("   • 圆形背景：8mm直径，#f7ab00颜色")
        print("   • 页码文字：白色，居中显示")
        print("   • 位置：底部黄条上方2mm")
        print("   • 单数页：左侧对齐")
        print("   • 双数页：右侧对齐")
    else:
        print("⚠️ 测试失败，请检查错误信息")
