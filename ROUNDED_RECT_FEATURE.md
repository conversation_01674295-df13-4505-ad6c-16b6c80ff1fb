# 双括号圆角矩形边框功能

## 功能描述

当文档中使用双括号"（（））"包围文本时，系统会自动为该文本添加圆角矩形边框样式。

## 实现细节

### 1. 语法格式
- 使用中文双括号：`（（文本内容））`
- 支持在段落中的任意位置使用
- 支持同一段落中多个双括号文本

### 2. 视觉效果
- 灰色边框（#888888）
- 圆角矩形（3px圆角）
- 白色背景
- 内边距：3px
- 边框宽度：1px

### 3. 代码实现

#### RoundedRectText 类
```python
class RoundedRectText(Flowable):
    """带圆角矩形边框的文本"""
    
    def __init__(self, text, style, border_color=None, border_width=1, corner_radius=5, padding=5):
        self.text = text
        self.style = style
        self.border_color = border_color or colors.Color(0.5, 0.5, 0.5)
        self.border_width = border_width
        self.corner_radius = corner_radius
        self.padding = padding
```

#### 段落处理逻辑
```python
def _process_paragraph_with_rounded_rect(self, text: str, styles: Dict[str, ParagraphStyle], story: List):
    """处理包含双括号圆角矩形文本的段落"""
    
    # 分割文本，找出双括号部分和普通部分
    parts = re.split(r'（（(.*?)））', text)
    
    # 创建水平布局来组合不同的元素
    # ...
```

## 使用示例

### 基本用法
```markdown
这是一个包含（（圆角矩形文本））的段落。
```

### 多个圆角矩形
```markdown
这个段落有（（第一个））和（（第二个））圆角矩形文本。
```

### 在列表中使用
```markdown
1. 列表项包含（（圆角矩形））文本
2. 另一个（（圆角矩形））列表项
```

### 与其他格式混合
```markdown
这个段落包含**粗体**、*斜体*和（（圆角矩形））文本。
```

## 技术特点

### 1. 自动检测
- 系统自动检测段落中的双括号语法
- 无需额外配置或设置

### 2. 灵活布局
- 使用ReportLab的Table组件实现水平布局
- 支持多个圆角矩形文本在同一行

### 3. 样式一致性
- 与文档整体样式保持一致
- 使用相同的字体和字号

### 4. 间距优化
- 圆角矩形文本与前面文字保持正常水平距离
- 右边距设置为2px，避免过大间隙
- 使用Table布局实现精确的水平对齐

### 5. 错误处理
- 如果圆角矩形绘制失败，会降级为普通文本
- 确保文档生成的稳定性

## 文件结构

```
backend/app/services/pdf_service.py
├── RoundedRectText 类                    # 圆角矩形文本Flowable
├── _process_paragraph_with_rounded_rect  # 段落处理方法
└── _markdown_to_pdf_elements            # 主要解析逻辑
```

## 测试文件

已生成的测试PDF文件：
- `generated_pdfs/rounded_rect_test.pdf` - 全面测试
- `generated_pdfs/simple_rounded_rect_test.pdf` - 简单测试
- `generated_pdfs/spacing_adjustment_test.pdf` - 间距调整测试

## 限制和注意事项

1. **嵌套限制**：不支持双括号嵌套，如`（（外层（（内层））））`
2. **换行处理**：圆角矩形文本不会自动换行，长文本可能超出边界
3. **字符限制**：必须使用中文双括号"（（））"，英文括号不会被识别

## 兼容性

- 与现有的编号列表功能完全兼容
- 与标题背景图片功能完全兼容
- 与其他Markdown格式（粗体、斜体、代码）兼容

## 示例效果

当您在文档中写入：
```
这是一个包含（（重要信息））的段落。
```

会渲染为：
```
这是一个包含 [重要信息] 的段落。
```
其中`[重要信息]`部分会显示为带圆角矩形边框的样式。
