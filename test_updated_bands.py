#!/usr/bin/env python3
"""
测试更新后的色条高度
顶部色条：1.0cm（原来的2倍）
底部色条：0.5cm（保持不变）
"""

import requests
import json
import base64

# 测试内容
test_content = """# 更新后的色条测试

这是一个用于测试更新后色条高度的文档。

## 色条规格

- **顶部色条**：高度 1.0cm（原来的2倍）
- **底部色条**：高度 0.5cm（保持不变）
- **颜色**：#ffe9a9（淡黄色）

## 测试说明

请检查生成的PDF文档：

1. 顶部色条应该比之前更高（1.0cm）
2. 底部色条保持原来的高度（0.5cm）
3. 两个色条都应该是 #ffe9a9 颜色
4. 文档内容应该正确显示在白色背景上

## 多页测试

为了测试多页文档的色条效果，这里添加一些额外的内容。

### 第一章 介绍

这是第一章的内容。每一页都应该有顶部和底部的色条。

### 第二章 详细说明

这是第二章的内容。色条应该在每一页上都保持一致。

### 第三章 代码示例

```python
def test_color_bands():
    # 测试色条功能
    top_band_height = 1.0  # cm
    bottom_band_height = 0.5  # cm
    color = "#ffe9a9"

    print(f"顶部色条高度: {top_band_height}cm")
    print(f"底部色条高度: {bottom_band_height}cm")
    print(f"色条颜色: {color}")

    return True
```

### 第四章 列表测试

1. 第一项内容
2. 第二项内容
3. 第三项内容
4. 第四项内容
5. 第五项内容

### 第五章 表格测试

| 项目 | 原高度 | 新高度 | 变化 |
|------|--------|--------|------|
| 顶部色条 | 0.5cm | 1.0cm | 增加2倍 |
| 底部色条 | 0.5cm | 0.5cm | 保持不变 |

## 结论

如果您看到顶部色条明显比底部色条更高，说明修改成功！
"""

# 配置
config = {
    "page_format": "A4",
    "margin_top": 2.0,
    "margin_bottom": 2.0,
    "margin_left": 2.0,
    "margin_right": 2.0,
    "font_family": "Noto Sans CJK SC",
    "font_size": 12,
    "line_height": 1.5,
    "paragraph_spacing": 6,
    "indent_first_line": True,
    "dpi": 300,
    "color_mode": "CMYK",
    "bleed": 3,
    "enable_hyphenation": True,
    "widow_orphan_control": True
}

def test_updated_bands():
    """测试更新后的色条高度"""
    url = "http://localhost:8000/api/pdf/preview"
    
    payload = {
        "content": test_content,
        "layout_config": config
    }
    
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()
        
        result = response.json()
        if result.get("success"):
            print("✅ 更新后的PDF预览生成成功")
            
            # 保存PDF文件用于查看
            pdf_data = result.get("pdf_data")
            if pdf_data:
                with open("test_updated_bands.pdf", "wb") as f:
                    f.write(base64.b64decode(pdf_data))
                print("📄 PDF文件已保存为 test_updated_bands.pdf")
                print("🔍 请打开PDF文件检查：")
                print("   - 顶部色条高度应为 1.0cm（比之前高2倍）")
                print("   - 底部色条高度应为 0.5cm（保持不变）")
                print("   - 两个色条都应该是 #ffe9a9 颜色")
            
            return True
        else:
            print("❌ PDF预览生成失败:", result.get("message", "未知错误"))
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 测试更新后的色条高度...")
    print("=" * 50)
    
    success = test_updated_bands()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 测试完成！")
        print("📋 修改总结：")
        print("   • 顶部色条高度：0.5cm → 1.0cm（增加2倍）")
        print("   • 底部色条高度：0.5cm → 0.5cm（保持不变）")
        print("   • 色条颜色：#ffe9a9（保持不变）")
    else:
        print("⚠️ 测试失败，请检查错误信息")
