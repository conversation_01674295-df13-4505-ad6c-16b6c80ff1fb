#!/usr/bin/env python3
"""
测试API序号样式功能
"""

import asyncio
import os
import sys
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.pdf_service import PDFService
from app.models.schemas import (
    LayoutConfig, BulletStyleConfig, NumberStyleConfig,
    BulletType, NumberBackgroundType, PDFGenerationRequest
)

async def test_api_bullet_functionality():
    """测试API序号功能"""
    
    print("=== 测试API序号样式功能 ===\n")
    
    # 测试内容
    test_content = """
# API序号样式测试

这个文档通过API配置来测试不同的序号样式。

## 有序列表测试

1. 第一个项目 - 测试数字序号样式
2. 第二个项目 - 包含**粗体**文字
3. 第三个项目 - 包含*斜体*文字
4. 第四个项目 - 包含`代码`文字
5. 第五个项目 - 最后一个项目

## 无序列表测试

- 第一个无序项目 - 测试项目符号样式
- 第二个无序项目 - 包含**粗体**文字
- 第三个无序项目 - 包含*斜体*文字
- 第四个无序项目 - 包含`代码`文字
- 第五个无序项目 - 最后一个项目

## 混合内容测试

这是一个普通段落，用于分隔不同的列表。

1. 混合内容中的有序列表项目一
2. 混合内容中的有序列表项目二
3. 混合内容中的有序列表项目三

这是另一个普通段落。

- 混合内容中的无序列表项目一
- 混合内容中的无序列表项目二
- 混合内容中的无序列表项目三

## 总结

通过这个测试，我们验证了：
1. API配置的序号样式功能
2. 不同类型的项目符号
3. 自定义颜色和大小
4. 在同一文档中的一致性
"""

    # 创建PDF服务实例
    pdf_service = PDFService()
    
    # 测试配置列表
    test_configs = [
        {
            "name": "默认样式",
            "bullet_style": None,
            "number_style": None,
            "filename": "api_default_test.pdf"
        },
        {
            "name": "蓝色圆形样式",
            "bullet_style": BulletStyleConfig(
                bullet_type=BulletType.CIRCLE,
                width=14,
                height=14,
                color="#0066cc"
            ),
            "number_style": NumberStyleConfig(
                background_type=NumberBackgroundType.CIRCLE,
                width=22,
                height=22,
                background_color="#0066cc",
                text_color="#ffffff",
                font_size=11
            ),
            "filename": "api_blue_circle_test.pdf"
        },
        {
            "name": "绿色方形样式",
            "bullet_style": BulletStyleConfig(
                bullet_type=BulletType.SQUARE,
                width=12,
                height=12,
                color="#00aa44"
            ),
            "number_style": NumberStyleConfig(
                background_type=NumberBackgroundType.SQUARE,
                width=20,
                height=20,
                background_color="#00aa44",
                text_color="#ffffff",
                font_size=10
            ),
            "filename": "api_green_square_test.pdf"
        },
        {
            "name": "紫色菱形样式",
            "bullet_style": BulletStyleConfig(
                bullet_type=BulletType.DIAMOND,
                width=15,
                height=15,
                color="#8844aa"
            ),
            "number_style": NumberStyleConfig(
                background_type=NumberBackgroundType.CIRCLE,
                width=24,
                height=24,
                background_color="#8844aa",
                text_color="#ffffff",
                font_size=12
            ),
            "filename": "api_purple_diamond_test.pdf"
        },
        {
            "name": "红色星形样式",
            "bullet_style": BulletStyleConfig(
                bullet_type=BulletType.STAR,
                width=16,
                height=16,
                color="#cc3300"
            ),
            "number_style": NumberStyleConfig(
                background_type=NumberBackgroundType.CIRCLE,
                width=26,
                height=26,
                background_color="#cc3300",
                text_color="#ffffff",
                font_size=14
            ),
            "filename": "api_red_star_test.pdf"
        }
    ]
    
    # 执行测试
    for i, test_config in enumerate(test_configs, 1):
        print(f"测试 {i}: {test_config['name']}")
        
        try:
            # 创建布局配置
            layout_config = LayoutConfig(
                page_format="A4",
                margin_top=2.0,
                margin_bottom=2.0,
                margin_left=2.0,
                margin_right=2.0,
                font_size=12,
                line_height=1.5,
                paragraph_spacing=12,
                indent_first_line=False,
                bullet_style=test_config["bullet_style"],
                number_style=test_config["number_style"]
            )
            
            # 生成PDF
            pdf_path = await pdf_service.generate_pdf(
                content=test_content,
                config=layout_config,
                filename=test_config["filename"]
            )
            
            # 检查文件
            if os.path.exists(pdf_path):
                file_size = os.path.getsize(pdf_path)
                print(f"   ✓ PDF生成成功: {pdf_path}")
                print(f"   ✓ 文件大小: {file_size} 字节")
            else:
                print(f"   ✗ PDF文件未生成")
                
        except Exception as e:
            print(f"   ✗ 生成失败: {e}")
            import traceback
            traceback.print_exc()
        
        print()
    
    print("=== API序号样式测试完成 ===")

if __name__ == '__main__':
    asyncio.run(test_api_bullet_functionality())
