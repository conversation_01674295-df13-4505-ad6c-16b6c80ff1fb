#!/usr/bin/env python3
"""
创建序号图片的脚本
"""

import os
from PIL import Image, ImageDraw, ImageFont

def create_bullet_image():
    """创建项目符号图片"""
    # 创建一个12x12的图片
    size = (12, 12)
    img = Image.new('RGBA', size, (0, 0, 0, 0))  # 透明背景
    draw = ImageDraw.Draw(img)
    
    # 绘制橙色圆点
    color = (247, 171, 0, 255)  # #f7ab00
    draw.ellipse([2, 2, 10, 10], fill=color)
    
    # 保存图片
    output_path = os.path.join('assets', 'bullet_image.png')
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    img.save(output_path)
    print(f"项目符号图片已保存到: {output_path}")

def create_number_background():
    """创建数字背景图片"""
    # 创建一个20x20的图片
    size = (20, 20)
    img = Image.new('RGBA', size, (0, 0, 0, 0))  # 透明背景
    draw = ImageDraw.Draw(img)
    
    # 绘制橙色圆形背景
    color = (247, 171, 0, 255)  # #f7ab00
    draw.ellipse([0, 0, 20, 20], fill=color)
    
    # 保存图片
    output_path = os.path.join('assets', 'number_background.png')
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    img.save(output_path)
    print(f"数字背景图片已保存到: {output_path}")

def create_alternative_bullets():
    """创建其他样式的项目符号"""
    styles = [
        {'name': 'square', 'shape': 'rectangle', 'color': (247, 171, 0, 255)},
        {'name': 'diamond', 'shape': 'diamond', 'color': (247, 171, 0, 255)},
        {'name': 'star', 'shape': 'star', 'color': (247, 171, 0, 255)},
    ]
    
    for style in styles:
        size = (12, 12)
        img = Image.new('RGBA', size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        if style['shape'] == 'rectangle':
            draw.rectangle([2, 2, 10, 10], fill=style['color'])
        elif style['shape'] == 'diamond':
            # 绘制菱形
            points = [(6, 2), (10, 6), (6, 10), (2, 6)]
            draw.polygon(points, fill=style['color'])
        elif style['shape'] == 'star':
            # 简化的星形（实际上是一个加号）
            draw.rectangle([5, 2, 7, 10], fill=style['color'])
            draw.rectangle([2, 5, 10, 7], fill=style['color'])
        
        output_path = os.path.join('assets', f'bullet_{style["name"]}.png')
        img.save(output_path)
        print(f"{style['name']}样式项目符号已保存到: {output_path}")

if __name__ == '__main__':
    create_bullet_image()
    create_number_background()
    create_alternative_bullets()
    print("所有序号图片创建完成！")
