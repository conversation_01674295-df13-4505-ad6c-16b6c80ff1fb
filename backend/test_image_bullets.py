#!/usr/bin/env python3
"""
测试图片序号功能
"""

import asyncio
import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.pdf_service import PDFService
from app.models.schemas import LayoutConfig

async def test_image_bullets():
    """测试图片序号功能"""
    
    # 创建PDF服务实例
    pdf_service = PDFService()
    
    # 创建测试内容
    test_content = """
# 图片序号测试文档

这是一个测试文档，用于验证图片序号功能。

## 有序列表测试

1. 第一个项目 - 这是一个有序列表项
2. 第二个项目 - 包含**粗体**文字
3. 第三个项目 - 包含*斜体*文字
4. 第四个项目 - 包含`代码`文字
5. 第五个项目 - 这是最后一个项目

## 无序列表测试

- 第一个无序项目
- 第二个无序项目 - 包含**粗体**文字
- 第三个无序项目 - 包含*斜体*文字
- 第四个无序项目 - 包含`代码`文字
- 第五个无序项目

## 混合内容测试

这是一个普通段落。

1. 有序列表项目一
2. 有序列表项目二

这是另一个普通段落。

- 无序列表项目一
- 无序列表项目二

## 嵌套内容测试

1. 主要项目一
   这是项目一的详细说明
2. 主要项目二
   这是项目二的详细说明
3. 主要项目三

- 主要无序项目一
  这是无序项目一的说明
- 主要无序项目二
  这是无序项目二的说明
"""

    # 创建布局配置
    config = LayoutConfig(
        page_format="A4",
        margin_top=2.0,
        margin_bottom=2.0,
        margin_left=2.0,
        margin_right=2.0,
        font_size=12,
        line_height=1.5,
        paragraph_spacing=12,
        indent_first_line=False
    )
    
    try:
        # 生成PDF
        print("开始生成PDF...")
        pdf_path = await pdf_service.generate_pdf(
            content=test_content,
            config=config,
            filename="image_bullets_test.pdf"
        )
        
        print(f"PDF生成成功: {pdf_path}")
        
        # 检查文件是否存在
        if os.path.exists(pdf_path):
            file_size = os.path.getsize(pdf_path)
            print(f"文件大小: {file_size} 字节")
        else:
            print("错误：PDF文件未生成")
            
    except Exception as e:
        print(f"生成PDF时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    asyncio.run(test_image_bullets())
