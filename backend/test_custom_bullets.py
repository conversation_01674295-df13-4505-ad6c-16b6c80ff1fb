#!/usr/bin/env python3
"""
测试自定义图片序号样式功能
"""

import asyncio
import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.pdf_service import PDFService, BulletStyle, NumberStyle
from app.models.schemas import LayoutConfig

async def test_custom_bullet_styles():
    """测试自定义序号样式功能"""
    
    # 创建PDF服务实例
    pdf_service = PDFService()
    
    # 测试内容
    test_content = """
# 自定义序号样式测试

## 默认样式测试

### 默认有序列表
1. 第一项
2. 第二项
3. 第三项

### 默认无序列表
- 第一项
- 第二项
- 第三项

## 自定义样式将在下一个测试中展示

这个测试展示了默认的序号样式。接下来我们将测试不同的自定义样式。

### 更多有序列表项目
1. 项目一 - 包含**粗体**文字
2. 项目二 - 包含*斜体*文字
3. 项目三 - 包含`代码`文字
4. 项目四 - 普通文字
5. 项目五 - 最后一项

### 更多无序列表项目
- 项目A - 包含**粗体**文字
- 项目B - 包含*斜体*文字
- 项目C - 包含`代码`文字
- 项目D - 普通文字
- 项目E - 最后一项
"""

    # 创建布局配置
    config = LayoutConfig(
        page_format="A4",
        margin_top=2.0,
        margin_bottom=2.0,
        margin_left=2.0,
        margin_right=2.0,
        font_size=12,
        line_height=1.5,
        paragraph_spacing=12,
        indent_first_line=False
    )
    
    try:
        # 测试1：默认样式
        print("测试1：生成默认样式PDF...")
        pdf_path1 = await pdf_service.generate_pdf(
            content=test_content,
            config=config,
            filename="default_bullets_test.pdf"
        )
        print(f"默认样式PDF生成成功: {pdf_path1}")
        
        # 测试2：自定义圆形样式（蓝色）
        print("\n测试2：生成蓝色圆形样式PDF...")
        blue_bullet_style = pdf_service.create_custom_bullet_style(
            bullet_type="circle",
            width=14,
            height=14,
            color="#0066cc"
        )
        blue_number_style = pdf_service.create_custom_number_style(
            background_type="circle",
            width=22,
            height=22,
            background_color="#0066cc",
            text_color="#ffffff",
            font_size=11
        )
        
        pdf_service.set_bullet_style(blue_bullet_style)
        pdf_service.set_number_style(blue_number_style)
        
        pdf_path2 = await pdf_service.generate_pdf(
            content=test_content,
            config=config,
            filename="blue_bullets_test.pdf"
        )
        print(f"蓝色样式PDF生成成功: {pdf_path2}")
        
        # 测试3：方形样式（绿色）
        print("\n测试3：生成绿色方形样式PDF...")
        green_bullet_style = pdf_service.create_custom_bullet_style(
            bullet_type="square",
            width=12,
            height=12,
            color="#00aa44"
        )
        green_number_style = pdf_service.create_custom_number_style(
            background_type="square",
            width=20,
            height=20,
            background_color="#00aa44",
            text_color="#ffffff",
            font_size=10
        )
        
        pdf_service.set_bullet_style(green_bullet_style)
        pdf_service.set_number_style(green_number_style)
        
        pdf_path3 = await pdf_service.generate_pdf(
            content=test_content,
            config=config,
            filename="green_square_bullets_test.pdf"
        )
        print(f"绿色方形样式PDF生成成功: {pdf_path3}")
        
        # 测试4：菱形样式（紫色）
        print("\n测试4：生成紫色菱形样式PDF...")
        purple_bullet_style = pdf_service.create_custom_bullet_style(
            bullet_type="diamond",
            width=14,
            height=14,
            color="#8844aa"
        )
        purple_number_style = pdf_service.create_custom_number_style(
            background_type="circle",
            width=24,
            height=24,
            background_color="#8844aa",
            text_color="#ffffff",
            font_size=12
        )
        
        pdf_service.set_bullet_style(purple_bullet_style)
        pdf_service.set_number_style(purple_number_style)
        
        pdf_path4 = await pdf_service.generate_pdf(
            content=test_content,
            config=config,
            filename="purple_diamond_bullets_test.pdf"
        )
        print(f"紫色菱形样式PDF生成成功: {pdf_path4}")
        
        print("\n所有测试完成！生成的PDF文件：")
        print(f"1. {pdf_path1}")
        print(f"2. {pdf_path2}")
        print(f"3. {pdf_path3}")
        print(f"4. {pdf_path4}")
        
    except Exception as e:
        print(f"生成PDF时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    asyncio.run(test_custom_bullet_styles())
