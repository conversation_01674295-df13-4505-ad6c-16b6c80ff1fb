# 图片序号功能使用指南

## 概述

PrintMind现在支持在PDF生成中使用自定义图片作为列表序号，包括有序列表的数字序号和无序列表的项目符号。

## 功能特性

### 1. 项目符号样式 (BulletStyle)
- **支持的类型**: 圆形、方形、菱形、星形、自定义图片
- **可配置属性**: 类型、图片路径、宽度、高度、颜色
- **默认样式**: 橙色圆形 (#f7ab00)

### 2. 数字序号样式 (NumberStyle)
- **支持的背景**: 圆形、方形、自定义图片
- **可配置属性**: 背景类型、图片路径、尺寸、背景色、文字色、字体大小
- **默认样式**: 橙色圆形背景，白色文字

## API使用方法

### 1. 基本配置

```python
from app.models.schemas import (
    LayoutConfig, BulletStyleConfig, NumberStyleConfig,
    BulletType, NumberBackgroundType
)

# 配置项目符号样式
bullet_style = BulletStyleConfig(
    bullet_type=BulletType.CIRCLE,  # 圆形
    width=14,
    height=14,
    color="#0066cc"  # 蓝色
)

# 配置数字序号样式
number_style = NumberStyleConfig(
    background_type=NumberBackgroundType.CIRCLE,  # 圆形背景
    width=22,
    height=22,
    background_color="#0066cc",  # 蓝色背景
    text_color="#ffffff",  # 白色文字
    font_size=11
)

# 应用到布局配置
layout_config = LayoutConfig(
    # ... 其他配置 ...
    bullet_style=bullet_style,
    number_style=number_style
)
```

### 2. 使用自定义图片

```python
# 使用自定义图片作为项目符号
custom_bullet_style = BulletStyleConfig(
    bullet_type=BulletType.IMAGE,
    image_path="path/to/your/bullet.png",
    width=16,
    height=16
)

# 使用自定义图片作为数字背景
custom_number_style = NumberStyleConfig(
    background_type=NumberBackgroundType.IMAGE,
    image_path="path/to/your/background.png",
    width=24,
    height=24,
    text_color="#ffffff",
    font_size=12
)
```

### 3. API端点

#### 获取可用样式
```
GET /api/pdf/bullet-styles
```

返回可用的序号样式选项：
```json
{
    "success": true,
    "bullet_types": [
        {"value": "circle", "label": "Circle"},
        {"value": "square", "label": "Square"},
        {"value": "diamond", "label": "Diamond"},
        {"value": "star", "label": "Star"},
        {"value": "image", "label": "Image"}
    ],
    "number_background_types": [
        {"value": "circle", "label": "Circle"},
        {"value": "square", "label": "Square"},
        {"value": "image", "label": "Image"}
    ],
    "available_images": [...],
    "default_colors": [...]
}
```

#### 测试序号样式
```
POST /api/pdf/test-bullets
```

生成包含各种列表的测试PDF来预览序号样式效果。

## 预定义样式

系统提供了以下预定义的图片样式：

1. **bullet_image.png** - 默认圆形项目符号
2. **bullet_square.png** - 方形项目符号
3. **bullet_diamond.png** - 菱形项目符号
4. **bullet_star.png** - 星形项目符号
5. **number_background.png** - 数字背景图片

## 编程接口

### PDFService类方法

```python
pdf_service = PDFService()

# 设置项目符号样式
bullet_style = pdf_service.create_custom_bullet_style(
    bullet_type="circle",
    width=14,
    height=14,
    color="#0066cc"
)
pdf_service.set_bullet_style(bullet_style)

# 设置数字样式
number_style = pdf_service.create_custom_number_style(
    background_type="circle",
    width=22,
    height=22,
    background_color="#0066cc",
    text_color="#ffffff",
    font_size=11
)
pdf_service.set_number_style(number_style)
```

## 示例代码

### 完整示例

```python
import asyncio
from app.services.pdf_service import PDFService
from app.models.schemas import LayoutConfig, BulletStyleConfig, NumberStyleConfig

async def generate_custom_bullet_pdf():
    # 创建自定义样式
    bullet_style = BulletStyleConfig(
        bullet_type="diamond",
        width=15,
        height=15,
        color="#8844aa"
    )
    
    number_style = NumberStyleConfig(
        background_type="circle",
        width=24,
        height=24,
        background_color="#8844aa",
        text_color="#ffffff",
        font_size=12
    )
    
    # 配置布局
    config = LayoutConfig(
        page_format="A4",
        font_size=12,
        bullet_style=bullet_style,
        number_style=number_style
    )
    
    # 生成PDF
    pdf_service = PDFService()
    pdf_path = await pdf_service.generate_pdf(
        content=markdown_content,
        config=config,
        filename="custom_bullets.pdf"
    )
    
    return pdf_path

# 运行示例
asyncio.run(generate_custom_bullet_pdf())
```

## 注意事项

1. **图片格式**: 支持PNG格式，建议使用透明背景
2. **图片尺寸**: 建议项目符号12-16px，数字背景20-26px
3. **颜色格式**: 使用十六进制颜色代码 (如 #ff6600)
4. **性能**: 自定义图片会增加PDF文件大小
5. **兼容性**: 所有样式都有降级方案，确保在图片缺失时仍能正常显示

## 故障排除

1. **图片不显示**: 检查图片路径是否正确，文件是否存在
2. **样式不生效**: 确认配置已正确传递给PDF生成方法
3. **文件过大**: 考虑优化图片尺寸或使用预定义样式

## 更新日志

- v1.0: 初始版本，支持基本的图片序号功能
- v1.1: 添加API配置支持
- v1.2: 增加预定义样式和测试端点
