"""
数据模型定义
"""

from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from enum import Enum

class DocumentType(str, Enum):
    """文档类型枚举"""
    MARKDOWN = "markdown"
    DOCX = "docx"
    TXT = "txt"

class PageFormat(str, Enum):
    """页面格式枚举"""
    A4 = "A4"
    A3 = "A3"
    LETTER = "Letter"
    LEGAL = "Legal"

class ColorMode(str, Enum):
    """颜色模式枚举"""
    RGB = "RGB"
    CMYK = "CMYK"

class BulletType(str, Enum):
    """项目符号类型枚举"""
    CIRCLE = "circle"
    SQUARE = "square"
    DIAMOND = "diamond"
    STAR = "star"
    IMAGE = "image"

class NumberBackgroundType(str, Enum):
    """数字背景类型枚举"""
    CIRCLE = "circle"
    SQUARE = "square"
    IMAGE = "image"

# 文档上传相关模型
class DocumentUploadResponse(BaseModel):
    """文档上传响应"""
    file_id: str
    filename: str
    file_type: DocumentType
    file_size: int
    markdown_content: str
    message: str

# 序号样式相关模型
class BulletStyleConfig(BaseModel):
    """项目符号样式配置"""
    bullet_type: BulletType = BulletType.CIRCLE
    image_path: Optional[str] = None
    width: int = Field(default=12, ge=8, le=24, description="宽度(px)")
    height: int = Field(default=12, ge=8, le=24, description="高度(px)")
    color: str = Field(default="#f7ab00", description="颜色(十六进制)")

class NumberStyleConfig(BaseModel):
    """数字序号样式配置"""
    background_type: NumberBackgroundType = NumberBackgroundType.CIRCLE
    image_path: Optional[str] = None
    width: int = Field(default=20, ge=16, le=32, description="宽度(px)")
    height: int = Field(default=20, ge=16, le=32, description="高度(px)")
    background_color: str = Field(default="#f7ab00", description="背景颜色(十六进制)")
    text_color: str = Field(default="#ffffff", description="文字颜色(十六进制)")
    font_size: int = Field(default=10, ge=8, le=16, description="字体大小(pt)")

# 排版配置相关模型
class LayoutConfig(BaseModel):
    """排版配置"""
    page_format: PageFormat = PageFormat.A4
    margin_top: float = Field(default=2.0, ge=0, le=5, description="上边距(cm)")
    margin_bottom: float = Field(default=2.0, ge=0, le=5, description="下边距(cm)")
    margin_left: float = Field(default=2.0, ge=0, le=5, description="左边距(cm)")
    margin_right: float = Field(default=2.0, ge=0, le=5, description="右边距(cm)")

    # 字体设置
    font_family: str = Field(default="Noto Sans CJK SC", description="字体族")
    font_size: float = Field(default=12, ge=8, le=24, description="字体大小(pt)")
    line_height: float = Field(default=1.5, ge=1.0, le=3.0, description="行高倍数")

    # 段落设置
    paragraph_spacing: float = Field(default=6, ge=0, le=20, description="段落间距(pt)")
    indent_first_line: bool = Field(default=True, description="首行缩进")

    # 序号样式设置
    bullet_style: Optional[BulletStyleConfig] = None
    number_style: Optional[NumberStyleConfig] = None

    # 印刷设置
    dpi: int = Field(default=300, ge=150, le=600, description="分辨率")
    color_mode: ColorMode = ColorMode.CMYK
    bleed: float = Field(default=3, ge=0, le=10, description="出血(mm)")

    # 高级设置
    enable_hyphenation: bool = Field(default=True, description="启用连字符")
    widow_orphan_control: bool = Field(default=True, description="孤行控制")

class AIOptimizationRequest(BaseModel):
    """AI优化请求"""
    content: str
    layout_config: LayoutConfig
    optimization_goals: List[str] = Field(
        default=["readability", "aesthetics", "print_quality"],
        description="优化目标"
    )

class AIOptimizationResponse(BaseModel):
    """AI优化响应"""
    optimized_config: LayoutConfig
    suggestions: List[str]
    confidence_score: float = Field(ge=0, le=1)
    reasoning: str

# PDF生成相关模型
class PDFGenerationRequest(BaseModel):
    """PDF生成请求"""
    content: str
    layout_config: LayoutConfig
    filename: Optional[str] = None

class PDFGenerationResponse(BaseModel):
    """PDF生成响应"""
    pdf_url: str
    file_size: int
    page_count: int
    generation_time: float
    message: str

# 字体相关模型
class FontInfo(BaseModel):
    """字体信息"""
    name: str
    family: str
    style: str
    file_path: str
    supports_chinese: bool

class FontListResponse(BaseModel):
    """字体列表响应"""
    fonts: List[FontInfo]
    total_count: int

# 通用响应模型
class BaseResponse(BaseModel):
    """基础响应模型"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None

class ErrorResponse(BaseModel):
    """错误响应模型"""
    success: bool = False
    error_code: str
    message: str
    details: Optional[Dict[str, Any]] = None
