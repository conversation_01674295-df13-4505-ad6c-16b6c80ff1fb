#!/usr/bin/env python3
"""
测试使用自定义图片作为序号的功能
"""

import asyncio
import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.pdf_service import PDFService, BulletStyle, NumberStyle
from app.models.schemas import LayoutConfig

async def test_custom_image_bullets():
    """测试使用自定义图片作为序号"""
    
    # 创建PDF服务实例
    pdf_service = PDFService()
    
    # 测试内容
    test_content = """
# 自定义图片序号测试

这个文档展示了如何使用自定义图片作为列表的序号。

## 使用星形图片的无序列表

- 第一个项目 - 使用星形图片作为项目符号
- 第二个项目 - 包含**粗体**文字
- 第三个项目 - 包含*斜体*文字
- 第四个项目 - 包含`代码`文字
- 第五个项目 - 最后一个项目

## 使用方形图片的无序列表

- 项目A - 使用方形图片作为项目符号
- 项目B - 这是第二个项目
- 项目C - 这是第三个项目
- 项目D - 这是第四个项目

## 使用自定义背景的有序列表

1. 第一项 - 使用自定义背景图片
2. 第二项 - 数字显示在图片上
3. 第三项 - 可以自定义颜色和大小
4. 第四项 - 支持不同的字体大小
5. 第五项 - 最后一项

## 混合使用

这里展示了在同一个文档中混合使用不同样式的列表：

### 菱形项目符号
- 菱形样式项目一
- 菱形样式项目二
- 菱形样式项目三

### 数字序号
1. 数字序号项目一
2. 数字序号项目二
3. 数字序号项目三

## 总结

通过这个测试，我们可以看到：
- 支持多种预定义的图片样式（圆形、方形、菱形、星形）
- 支持自定义图片作为序号
- 支持自定义颜色和大小
- 支持在同一文档中使用不同的样式
"""

    # 创建布局配置
    config = LayoutConfig(
        page_format="A4",
        margin_top=2.0,
        margin_bottom=2.0,
        margin_left=2.0,
        margin_right=2.0,
        font_size=12,
        line_height=1.5,
        paragraph_spacing=12,
        indent_first_line=False
    )
    
    try:
        # 测试1：使用星形图片作为项目符号
        print("测试1：使用星形图片作为项目符号...")
        star_bullet_style = pdf_service.create_custom_bullet_style(
            bullet_type="star",
            width=14,
            height=14,
            color="#ff6600"
        )
        
        pdf_service.set_bullet_style(star_bullet_style)
        
        pdf_path1 = await pdf_service.generate_pdf(
            content=test_content,
            config=config,
            filename="star_bullets_test.pdf"
        )
        print(f"星形项目符号PDF生成成功: {pdf_path1}")
        
        # 测试2：使用方形图片和大号数字背景
        print("\n测试2：使用方形图片和大号数字背景...")
        square_bullet_style = pdf_service.create_custom_bullet_style(
            bullet_type="square",
            width=16,
            height=16,
            color="#cc3300"
        )
        large_number_style = pdf_service.create_custom_number_style(
            background_type="circle",
            width=26,
            height=26,
            background_color="#cc3300",
            text_color="#ffffff",
            font_size=14
        )
        
        pdf_service.set_bullet_style(square_bullet_style)
        pdf_service.set_number_style(large_number_style)
        
        pdf_path2 = await pdf_service.generate_pdf(
            content=test_content,
            config=config,
            filename="square_large_bullets_test.pdf"
        )
        print(f"方形大号样式PDF生成成功: {pdf_path2}")
        
        # 测试3：使用菱形图片和方形数字背景
        print("\n测试3：使用菱形图片和方形数字背景...")
        diamond_bullet_style = pdf_service.create_custom_bullet_style(
            bullet_type="diamond",
            width=15,
            height=15,
            color="#9933cc"
        )
        square_number_style = pdf_service.create_custom_number_style(
            background_type="square",
            width=22,
            height=22,
            background_color="#9933cc",
            text_color="#ffffff",
            font_size=11
        )
        
        pdf_service.set_bullet_style(diamond_bullet_style)
        pdf_service.set_number_style(square_number_style)
        
        pdf_path3 = await pdf_service.generate_pdf(
            content=test_content,
            config=config,
            filename="diamond_square_bullets_test.pdf"
        )
        print(f"菱形方形样式PDF生成成功: {pdf_path3}")
        
        print("\n所有自定义图片序号测试完成！生成的PDF文件：")
        print(f"1. {pdf_path1}")
        print(f"2. {pdf_path2}")
        print(f"3. {pdf_path3}")
        
        # 检查文件大小
        for i, path in enumerate([pdf_path1, pdf_path2, pdf_path3], 1):
            if os.path.exists(path):
                size = os.path.getsize(path)
                print(f"   文件{i}大小: {size} 字节")
        
    except Exception as e:
        print(f"生成PDF时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    asyncio.run(test_custom_image_bullets())
