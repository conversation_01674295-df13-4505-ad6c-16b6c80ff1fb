#!/usr/bin/env python3
"""
清理测试文件脚本
"""

import os
import glob

def cleanup_test_files():
    """清理测试生成的文件"""
    
    print("开始清理测试文件...")
    
    # 要清理的文件模式
    cleanup_patterns = [
        "test_*.py",
        "create_bullet_images.py",
        "generated_pdfs/*test*.pdf",
        "generated_pdfs/*bullets*.pdf"
    ]
    
    cleaned_files = []
    
    for pattern in cleanup_patterns:
        files = glob.glob(pattern)
        for file_path in files:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    cleaned_files.append(file_path)
                    print(f"已删除: {file_path}")
            except Exception as e:
                print(f"删除失败 {file_path}: {e}")
    
    print(f"\n清理完成！共删除 {len(cleaned_files)} 个文件。")
    
    # 保留的重要文件
    important_files = [
        "BULLET_STYLES_README.md",
        "assets/bullet_*.png",
        "assets/number_background.png"
    ]
    
    print("\n保留的重要文件:")
    for pattern in important_files:
        files = glob.glob(pattern)
        for file_path in files:
            if os.path.exists(file_path):
                print(f"  ✓ {file_path}")

if __name__ == '__main__':
    cleanup_test_files()
