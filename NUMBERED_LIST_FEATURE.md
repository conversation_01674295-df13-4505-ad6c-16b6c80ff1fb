# 编号列表橙色圆形背景功能

## 功能描述

当文档中使用编号列表（1. 2. 3. 等格式）时，系统会自动为每个编号应用橙色圆形背景图片。

## 实现细节

### 1. 背景图片
- 位置：`backend/assets/numbered_list_background.png`
- 格式：PNG，40x40像素
- 颜色：橙色 (#FF8C00)
- 形状：圆形

### 2. 代码实现
- 新增了 `NumberedListItem` 类来处理带背景图片的编号列表项
- 修改了 `_markdown_to_pdf_elements` 方法来识别编号列表
- 使用正则表达式 `^\d+\.\s+` 来匹配编号列表格式

### 3. 支持的格式
- 基本编号：`1. 列表项`
- 多位数编号：`10. 列表项`、`123. 列表项`
- 带格式文本：`1. **粗体**文本`、`2. *斜体*文本`、`3. `代码`文本`

### 4. 不支持的格式
- 无空格：`1.列表项`（必须有空格）
- 缩进：` 1. 列表项`（不能有前导空格）
- 字母：`a. 列表项`（只支持数字）
- 括号：`1) 列表项`（必须使用点号）

## 使用示例

```markdown
# 示例文档

## 编号列表

1. 第一个列表项
2. 第二个列表项
3. 第三个列表项

## 带格式的编号列表

1. **粗体**文本
2. *斜体*文本
3. `代码`文本
```

## 视觉效果

每个编号会显示为：
- 白色数字
- 橙色圆形背景
- 左侧对齐，距离左边10px（已向左调整5px）
- 列表项文本在圆形右侧10px处开始

## 技术实现

### NumberedListItem 类
```python
class NumberedListItem(Flowable):
    """带背景图片的编号列表项"""
    
    def __init__(self, number, text, style, background_image_path=None):
        self.number = number
        self.text = text
        self.style = style
        self.background_image_path = background_image_path
```

### 正则表达式匹配
```python
# 匹配编号列表
if re.match(r'^\d+\.\s+', line):
    match = re.match(r'^(\d+)\.\s+(.*)', line)
    if match:
        number = int(match.group(1))
        text = match.group(2).strip()
```

## 文件结构

```
backend/
├── assets/
│   ├── numbered_list_background.png  # 橙色圆形背景图片
│   └── numbered_list_background.svg  # SVG版本（备用）
└── app/
    └── services/
        └── pdf_service.py  # 主要实现代码
```

## 测试

已生成的测试PDF文件：
- `generated_pdfs/final_numbered_list_test.pdf`
- `generated_pdfs/comprehensive_numbered_list_test.pdf`

这些文件展示了编号列表的各种使用场景和视觉效果。
