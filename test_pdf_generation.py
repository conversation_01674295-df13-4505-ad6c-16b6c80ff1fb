#!/usr/bin/env python3
"""
测试PDF生成功能，验证色条是否正确显示
"""

import requests
import json
import base64

# 测试内容
test_content = """# 测试文档

这是一个用于测试色条功能的简单文档。

## 第一章 介绍

这是一个普通段落，用于测试PDF生成功能是否正常工作。我们需要确保顶部和底部的色条能够正确显示。

### 1.1 功能特点

- 顶部色条：#ffe9a9 颜色
- 底部色条：#ffe9a9 颜色  
- 中间内容：白色背景

## 第二章 测试内容

这里是更多的测试内容，用于验证多页文档的色条显示效果。

### 2.1 代码示例

```python
def test_function():
    print("这是一个测试函数")
    return True
```

### 2.2 列表测试

1. 第一项
2. 第二项
3. 第三项

## 结论

如果您看到这个PDF文档的顶部和底部都有淡黄色的色条，那么功能就正常工作了。
"""

# 配置
config = {
    "page_format": "A4",
    "margin_top": 2.0,
    "margin_bottom": 2.0,
    "margin_left": 2.0,
    "margin_right": 2.0,
    "font_family": "Noto Sans CJK SC",
    "font_size": 12,
    "line_height": 1.5,
    "paragraph_spacing": 6,
    "indent_first_line": True,
    "dpi": 300,
    "color_mode": "CMYK",
    "bleed": 3,
    "enable_hyphenation": True,
    "widow_orphan_control": True
}

def test_pdf_preview():
    """测试PDF预览功能"""
    url = "http://localhost:8000/api/pdf/preview"
    
    payload = {
        "content": test_content,
        "layout_config": config
    }
    
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()
        
        result = response.json()
        if result.get("success"):
            print("✅ PDF预览生成成功")
            
            # 保存PDF文件用于查看
            pdf_data = result.get("pdf_data")
            if pdf_data:
                with open("test_output.pdf", "wb") as f:
                    f.write(base64.b64decode(pdf_data))
                print("📄 PDF文件已保存为 test_output.pdf")
            
            return True
        else:
            print("❌ PDF预览生成失败:", result.get("message", "未知错误"))
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        return False

def test_pdf_generation():
    """测试PDF生成功能"""
    url = "http://localhost:8000/api/pdf/generate"
    
    payload = {
        "content": test_content,
        "layout_config": config,
        "filename": "test_color_bands.pdf"
    }
    
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()
        
        result = response.json()
        print("✅ PDF生成成功")
        print(f"📄 文件大小: {result.get('file_size', 0)} bytes")
        print(f"📊 页数: {result.get('page_count', 0)}")
        print(f"⏱️ 生成时间: {result.get('generation_time', 0):.2f} 秒")
        print(f"🔗 下载链接: http://localhost:8000{result.get('pdf_url', '')}")
        
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 开始测试PDF生成功能...")
    print("=" * 50)
    
    # 测试预览功能
    print("\n1. 测试PDF预览功能:")
    preview_success = test_pdf_preview()
    
    # 测试生成功能
    print("\n2. 测试PDF生成功能:")
    generate_success = test_pdf_generation()
    
    print("\n" + "=" * 50)
    if preview_success and generate_success:
        print("🎉 所有测试通过！")
        print("💡 请检查生成的PDF文件，确认顶部和底部是否有 #ffe9a9 颜色的色条")
    else:
        print("⚠️ 部分测试失败，请检查错误信息")
